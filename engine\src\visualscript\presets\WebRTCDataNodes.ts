/**
 * 视觉脚本WebRTC数据通信节点
 * 提供WebRTC点对点数据传输功能
 */
import { AsyncNode, AsyncNodeOptions } from '../nodes/AsyncNode';
import { EventNode, EventNodeOptions } from '../nodes/EventNode';
import { FlowNode, FlowNodeOptions } from '../nodes/FlowNode';
import { NodeCategory, SocketDirection, SocketType, NodeOptions } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';

/**
 * WebRTC连接管理器
 */
class WebRTCManager {
  private static instance: WebRTCManager;
  private connections: Map<string, RTCPeerConnection> = new Map();
  private dataChannels: Map<string, RTCDataChannel> = new Map();

  public static getInstance(): WebRTCManager {
    if (!WebRTCManager.instance) {
      WebRTCManager.instance = new WebRTCManager();
    }
    return WebRTCManager.instance;
  }

  public async createConnection(peerId: string, config?: RTCConfiguration): Promise<RTCPeerConnection> {
    const defaultConfig: RTCConfiguration = {
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' }
      ]
    };

    const pc = new RTCPeerConnection(config || defaultConfig);
    this.connections.set(peerId, pc);

    // 设置连接状态监听
    pc.onconnectionstatechange = () => {
      console.log(`WebRTC连接状态变化 [${peerId}]:`, pc.connectionState);
      if (pc.connectionState === 'closed' || pc.connectionState === 'failed') {
        this.connections.delete(peerId);
        this.dataChannels.delete(peerId);
      }
    };

    return pc;
  }

  public getConnection(peerId: string): RTCPeerConnection | undefined {
    return this.connections.get(peerId);
  }

  public createDataChannel(peerId: string, label: string, options?: RTCDataChannelInit): RTCDataChannel | null {
    const pc = this.connections.get(peerId);
    if (!pc) {
      console.error(`WebRTC连接不存在: ${peerId}`);
      return null;
    }

    const dataChannel = pc.createDataChannel(label, options);
    this.dataChannels.set(peerId, dataChannel);
    return dataChannel;
  }

  public getDataChannel(peerId: string): RTCDataChannel | undefined {
    return this.dataChannels.get(peerId);
  }

  public closeConnection(peerId: string): void {
    const pc = this.connections.get(peerId);
    if (pc) {
      pc.close();
      this.connections.delete(peerId);
      this.dataChannels.delete(peerId);
    }
  }

  public closeAllConnections(): void {
    for (const [peerId, pc] of this.connections.entries()) {
      pc.close();
    }
    this.connections.clear();
    this.dataChannels.clear();
  }
}

/**
 * WebRTC连接创建节点
 * 创建WebRTC连接
 */
export class WebRTCCreateConnectionNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'peerId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '对等方ID',
      defaultValue: 'peer1'
    });

    this.addInput({
      name: 'iceServers',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: 'ICE服务器列表',
      defaultValue: [{ urls: 'stun:stun.l.google.com:19302' }],
      optional: true
    });

    this.addInput({
      name: 'channelLabel',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '数据通道标签',
      defaultValue: 'dataChannel',
      optional: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'connected',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '连接成功流程'
    });

    this.addOutput({
      name: 'failed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '连接失败流程'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'connection',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: 'WebRTC连接对象'
    });

    this.addOutput({
      name: 'dataChannel',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '数据通道对象'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  /**
   * 执行异步操作
   * @param inputs 输入值
   * @returns Promise对象
   */
  protected async executeAsync(inputs: Record<string, any>): Promise<any> {
    const peerId = inputs.peerId as string;
    const iceServers = inputs.iceServers as RTCIceServer[];
    const channelLabel = inputs.channelLabel as string || 'dataChannel';

    // 验证输入
    if (!peerId) {
      this.setOutputValue('error', '对等方ID不能为空');
      this.triggerFlow('failed');
      return false;
    }

    try {
      const manager = WebRTCManager.getInstance();
      
      // 创建WebRTC连接
      const config: RTCConfiguration = {
        iceServers: iceServers || [{ urls: 'stun:stun.l.google.com:19302' }]
      };
      
      const pc = await manager.createConnection(peerId, config);
      
      // 创建数据通道
      const dataChannel = manager.createDataChannel(peerId, channelLabel, {
        ordered: true
      });

      if (!dataChannel) {
        this.setOutputValue('error', '创建数据通道失败');
        this.triggerFlow('failed');
        return false;
      }

      // 等待数据通道打开
      await new Promise<void>((resolve, reject) => {
        dataChannel.onopen = () => resolve();
        dataChannel.onerror = (error) => reject(error);
        
        // 设置超时
        setTimeout(() => reject(new Error('数据通道打开超时')), 10000);
      });

      // 设置输出值
      this.setOutputValue('connection', pc);
      this.setOutputValue('dataChannel', dataChannel);

      // 触发连接成功流程
      this.triggerFlow('connected');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      console.error('WebRTC连接创建失败:', errorMessage);
      
      // 设置错误输出
      this.setOutputValue('error', errorMessage);
      
      // 触发连接失败流程
      this.triggerFlow('failed');
      return false;
    }
  }
}

/**
 * WebRTC发送数据节点
 * 通过WebRTC发送数据
 */
export class WebRTCSendDataNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const flowOptions: FlowNodeOptions = {
      ...options,
      inputFlowName: 'flow',
      outputFlowNames: ['success', 'fail']
    };
    super(flowOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加输入数据插槽
    this.addInput({
      name: 'peerId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '对等方ID',
      defaultValue: 'peer1'
    });

    this.addInput({
      name: 'data',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.INPUT,
      description: '要发送的数据'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const peerId = this.getInputValue('peerId') as string;
    const data = this.getInputValue('data');

    // 验证输入
    if (!peerId) {
      this.setOutputValue('error', '对等方ID不能为空');
      this.triggerFlow('fail');
      return false;
    }

    if (data === undefined || data === null) {
      this.setOutputValue('error', '数据不能为空');
      this.triggerFlow('fail');
      return false;
    }

    try {
      const manager = WebRTCManager.getInstance();
      const dataChannel = manager.getDataChannel(peerId);

      if (!dataChannel) {
        this.setOutputValue('error', 'WebRTC数据通道不存在，请先建立连接');
        this.triggerFlow('fail');
        return false;
      }

      if (dataChannel.readyState !== 'open') {
        this.setOutputValue('error', 'WebRTC数据通道未打开');
        this.triggerFlow('fail');
        return false;
      }

      // 发送数据
      const dataStr = typeof data === 'string' ? data : JSON.stringify(data);
      dataChannel.send(dataStr);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      console.error('WebRTC发送数据失败:', errorMessage);
      
      // 设置错误输出
      this.setOutputValue('error', errorMessage);
      
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * WebRTC接收数据节点
 * 监听WebRTC数据接收
 */
export class WebRTCOnDataReceivedNode extends EventNode {
  private messageListener?: (event: MessageEvent) => void;

  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const eventOptions: EventNodeOptions = {
      ...options,
      outputFlowName: 'onDataReceived'
    };
    super(eventOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加输入数据插槽
    this.addInput({
      name: 'peerId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '对等方ID',
      defaultValue: 'peer1'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'data',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.OUTPUT,
      description: '接收到的数据'
    });

    this.addOutput({
      name: 'rawData',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '原始数据'
    });

    this.addOutput({
      name: 'peerId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '发送方ID'
    });
  }

  /**
   * 初始化节点
   */
  public initialize(): void {
    super.initialize();
    this.setupDataListener();
  }

  /**
   * 设置数据监听器
   */
  private setupDataListener(): void {
    const peerId = this.getInputValue('peerId') as string;

    if (!peerId) {
      console.warn('对等方ID为空，无法设置数据监听器');
      return;
    }

    const manager = WebRTCManager.getInstance();
    const dataChannel = manager.getDataChannel(peerId);

    if (!dataChannel) {
      console.warn('WebRTC数据通道不存在，无法设置数据监听器');
      return;
    }

    // 移除旧的监听器
    if (this.messageListener) {
      dataChannel.removeEventListener('message', this.messageListener);
    }

    // 创建新的监听器
    this.messageListener = (event: MessageEvent) => {
      const rawData = event.data;
      let parsedData: any;

      try {
        parsedData = JSON.parse(rawData);
      } catch {
        parsedData = rawData;
      }

      // 设置输出值
      this.setOutputValue('data', parsedData);
      this.setOutputValue('rawData', rawData);
      this.setOutputValue('peerId', peerId);

      // 触发数据接收事件
      this.triggerFlow('onDataReceived');
    };

    // 添加监听器
    dataChannel.addEventListener('message', this.messageListener);
  }

  /**
   * 销毁节点
   */
  public dispose(): void {
    const peerId = this.getInputValue('peerId') as string;

    if (peerId && this.messageListener) {
      const manager = WebRTCManager.getInstance();
      const dataChannel = manager.getDataChannel(peerId);

      if (dataChannel) {
        dataChannel.removeEventListener('message', this.messageListener);
      }
    }

    super.dispose();
  }
}

/**
 * 注册WebRTC数据通信节点
 * @param registry 节点注册表
 */
export function registerWebRTCDataNodes(registry: NodeRegistry): void {
  // 注册WebRTC连接创建节点
  registry.registerNodeType({
    type: 'network/webrtc/createConnection',
    category: NodeCategory.NETWORK,
    constructor: WebRTCCreateConnectionNode,
    label: 'WebRTC连接',
    description: '创建WebRTC连接',
    icon: 'webrtc',
    color: '#00BCD4',
    tags: ['network', 'webrtc', 'connection']
  });

  // 注册WebRTC发送数据节点
  registry.registerNodeType({
    type: 'network/webrtc/sendData',
    category: NodeCategory.NETWORK,
    constructor: WebRTCSendDataNode,
    label: 'WebRTC发送数据',
    description: '通过WebRTC发送数据',
    icon: 'send',
    color: '#00BCD4',
    tags: ['network', 'webrtc', 'send', 'data']
  });

  // 注册WebRTC接收数据节点
  registry.registerNodeType({
    type: 'network/webrtc/onDataReceived',
    category: NodeCategory.NETWORK,
    constructor: WebRTCOnDataReceivedNode,
    label: 'WebRTC接收数据',
    description: '监听WebRTC数据接收',
    icon: 'receive',
    color: '#00BCD4',
    tags: ['network', 'webrtc', 'receive', 'data', 'event']
  });

  console.log('已注册所有WebRTC数据通信节点类型');
}
