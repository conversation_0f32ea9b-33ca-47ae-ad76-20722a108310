/**
 * 新数组和对象节点单元测试
 * 测试新实现的数组操作和对象操作节点
 */
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { NodeRegistry } from '../../src/visualscript/nodes/NodeRegistry';
import {
  ArrayShiftNode,
  ArrayUnshiftNode,
  ArrayLengthNode,
  ArrayGetNode,
  ArraySetNode,
  ArrayIndexOfNode,
  ArrayContainsNode,
  ArraySliceNode,
  ArrayJoinNode,
  ArrayReverseNode,
  ArraySortNode
} from '../../src/visualscript/presets/ArrayNodes';
import {
  ObjectCreateNode,
  ObjectHasPropertyNode
} from '../../src/visualscript/presets/ObjectNodes';

describe('新数组操作节点测试', () => {
  let registry: NodeRegistry;

  beforeEach(() => {
    registry = new NodeRegistry();
  });

  afterEach(() => {
    registry.clear();
  });

  describe('数组移除首元素节点', () => {
    it('应该正确移除并返回首个元素', () => {
      const node = new ArrayShiftNode({
        id: 'shift-test',
        type: 'array/shift',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'array') return ['a', 'b', 'c'];
        return [];
      };

      let resultArray: any[];
      let shiftedElement: any;
      let arrayLength: number;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') resultArray = value;
        if (name === 'element') shiftedElement = value;
        if (name === 'length') arrayLength = value;
      };

      const result = node.execute();
      expect(result).toEqual(['b', 'c']);
      expect(resultArray!).toEqual(['b', 'c']);
      expect(shiftedElement!).toBe('a');
      expect(arrayLength!).toBe(2);
    });

    it('应该处理空数组', () => {
      const node = new ArrayShiftNode({
        id: 'shift-test',
        type: 'array/shift',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'array') return [];
        return [];
      };

      let resultArray: any[];
      let shiftedElement: any;
      let arrayLength: number;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') resultArray = value;
        if (name === 'element') shiftedElement = value;
        if (name === 'length') arrayLength = value;
      };

      const result = node.execute();
      expect(result).toEqual([]);
      expect(resultArray!).toEqual([]);
      expect(shiftedElement!).toBeUndefined();
      expect(arrayLength!).toBe(0);
    });
  });

  describe('数组添加首元素节点', () => {
    it('应该正确在开头添加元素', () => {
      const node = new ArrayUnshiftNode({
        id: 'unshift-test',
        type: 'array/unshift',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'array') return ['b', 'c'];
        if (name === 'element') return 'a';
        return [];
      };

      let resultArray: any[];
      let arrayLength: number;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') resultArray = value;
        if (name === 'length') arrayLength = value;
      };

      const result = node.execute();
      expect(result).toEqual(['a', 'b', 'c']);
      expect(resultArray!).toEqual(['a', 'b', 'c']);
      expect(arrayLength!).toBe(3);
    });
  });

  describe('数组长度节点', () => {
    it('应该正确获取数组长度', () => {
      const node = new ArrayLengthNode({
        id: 'length-test',
        type: 'array/length',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'array') return ['a', 'b', 'c', 'd'];
        return [];
      };

      let arrayLength: number;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'length') arrayLength = value;
      };

      const result = node.execute();
      expect(result).toBe(4);
      expect(arrayLength!).toBe(4);
    });

    it('应该处理空数组', () => {
      const node = new ArrayLengthNode({
        id: 'length-test',
        type: 'array/length',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'array') return [];
        return [];
      };

      let arrayLength: number;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'length') arrayLength = value;
      };

      const result = node.execute();
      expect(result).toBe(0);
      expect(arrayLength!).toBe(0);
    });
  });

  describe('数组获取元素节点', () => {
    it('应该正确获取指定索引的元素', () => {
      const node = new ArrayGetNode({
        id: 'get-test',
        type: 'array/get',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'array') return ['a', 'b', 'c'];
        if (name === 'index') return 1;
        if (name === 'defaultValue') return 'default';
        return 0;
      };

      let element: any;
      let isValid: boolean;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'element') element = value;
        if (name === 'valid') isValid = value;
      };

      const result = node.execute();
      expect(result).toBe('b');
      expect(element!).toBe('b');
      expect(isValid!).toBe(true);
    });

    it('应该处理无效索引', () => {
      const node = new ArrayGetNode({
        id: 'get-test',
        type: 'array/get',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'array') return ['a', 'b', 'c'];
        if (name === 'index') return 5;
        if (name === 'defaultValue') return 'default';
        return 0;
      };

      let element: any;
      let isValid: boolean;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'element') element = value;
        if (name === 'valid') isValid = value;
      };

      const result = node.execute();
      expect(result).toBe('default');
      expect(element!).toBe('default');
      expect(isValid!).toBe(false);
    });
  });

  describe('数组设置元素节点', () => {
    it('应该正确设置指定索引的元素', () => {
      const node = new ArraySetNode({
        id: 'set-test',
        type: 'array/set',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'array') return ['a', 'b', 'c'];
        if (name === 'index') return 1;
        if (name === 'value') return 'x';
        return 0;
      };

      let resultArray: any[];
      let success: boolean;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') resultArray = value;
        if (name === 'success') success = value;
      };

      const result = node.execute();
      expect(result).toEqual(['a', 'x', 'c']);
      expect(resultArray!).toEqual(['a', 'x', 'c']);
      expect(success!).toBe(true);
    });

    it('应该扩展数组当索引超出范围时', () => {
      const node = new ArraySetNode({
        id: 'set-test',
        type: 'array/set',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'array') return ['a', 'b'];
        if (name === 'index') return 4;
        if (name === 'value') return 'x';
        return 0;
      };

      let resultArray: any[];
      let success: boolean;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') resultArray = value;
        if (name === 'success') success = value;
      };

      const result = node.execute();
      expect(result).toEqual(['a', 'b', undefined, undefined, 'x']);
      expect(resultArray!).toEqual(['a', 'b', undefined, undefined, 'x']);
      expect(success!).toBe(true);
    });
  });

  describe('数组查找索引节点', () => {
    it('应该正确查找元素索引', () => {
      const node = new ArrayIndexOfNode({
        id: 'indexOf-test',
        type: 'array/indexOf',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'array') return ['a', 'b', 'c', 'b'];
        if (name === 'element') return 'b';
        if (name === 'startIndex') return 0;
        return 0;
      };

      let index: number;
      let found: boolean;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'index') index = value;
        if (name === 'found') found = value;
      };

      const result = node.execute();
      expect(index!).toBe(1);
      expect(found!).toBe(true);
    });

    it('应该返回-1当元素不存在时', () => {
      const node = new ArrayIndexOfNode({
        id: 'indexOf-test',
        type: 'array/indexOf',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'array') return ['a', 'b', 'c'];
        if (name === 'element') return 'x';
        if (name === 'startIndex') return 0;
        return 0;
      };

      let index: number;
      let found: boolean;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'index') index = value;
        if (name === 'found') found = value;
      };

      const result = node.execute();
      expect(index!).toBe(-1);
      expect(found!).toBe(false);
    });
  });

  describe('数组包含检查节点', () => {
    it('应该正确检查数组是否包含元素', () => {
      const node = new ArrayContainsNode({
        id: 'contains-test',
        type: 'array/contains',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'array') return ['a', 'b', 'c'];
        if (name === 'element') return 'b';
        return [];
      };

      let contains: boolean;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'contains') contains = value;
      };

      const result = node.execute();
      expect(result).toBe(true);
      expect(contains!).toBe(true);
    });

    it('应该返回false当元素不存在时', () => {
      const node = new ArrayContainsNode({
        id: 'contains-test',
        type: 'array/contains',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'array') return ['a', 'b', 'c'];
        if (name === 'element') return 'x';
        return [];
      };

      let contains: boolean;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'contains') contains = value;
      };

      const result = node.execute();
      expect(result).toBe(false);
      expect(contains!).toBe(false);
    });
  });

  describe('数组切片节点', () => {
    it('应该正确切片数组', () => {
      const node = new ArraySliceNode({
        id: 'slice-test',
        type: 'array/slice',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'array') return ['a', 'b', 'c', 'd', 'e'];
        if (name === 'start') return 1;
        if (name === 'end') return 4;
        return 0;
      };

      let resultArray: any[];
      let arrayLength: number;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') resultArray = value;
        if (name === 'length') arrayLength = value;
      };

      const result = node.execute();
      expect(result).toEqual(['b', 'c', 'd']);
      expect(resultArray!).toEqual(['b', 'c', 'd']);
      expect(arrayLength!).toBe(3);
    });

    it('应该从指定位置切片到末尾', () => {
      const node = new ArraySliceNode({
        id: 'slice-test',
        type: 'array/slice',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'array') return ['a', 'b', 'c', 'd', 'e'];
        if (name === 'start') return 2;
        if (name === 'end') return -1;
        return 0;
      };

      let resultArray: any[];
      let arrayLength: number;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') resultArray = value;
        if (name === 'length') arrayLength = value;
      };

      const result = node.execute();
      expect(result).toEqual(['c', 'd', 'e']);
      expect(resultArray!).toEqual(['c', 'd', 'e']);
      expect(arrayLength!).toBe(3);
    });
  });

  describe('数组连接节点', () => {
    it('应该正确连接数组元素为字符串', () => {
      const node = new ArrayJoinNode({
        id: 'join-test',
        type: 'array/join',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'array') return ['a', 'b', 'c'];
        if (name === 'separator') return '-';
        return '';
      };

      let result: string;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') result = value;
      };

      const output = node.execute();
      expect(output).toBe('a-b-c');
      expect(result!).toBe('a-b-c');
    });

    it('应该使用默认分隔符', () => {
      const node = new ArrayJoinNode({
        id: 'join-test',
        type: 'array/join',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'array') return ['a', 'b', 'c'];
        if (name === 'separator') return ',';
        return '';
      };

      let result: string;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') result = value;
      };

      const output = node.execute();
      expect(output).toBe('a,b,c');
      expect(result!).toBe('a,b,c');
    });
  });

  describe('数组反转节点', () => {
    it('应该正确反转数组', () => {
      const node = new ArrayReverseNode({
        id: 'reverse-test',
        type: 'array/reverse',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'array') return ['a', 'b', 'c', 'd'];
        return [];
      };

      let resultArray: any[];
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') resultArray = value;
      };

      const result = node.execute();
      expect(result).toEqual(['d', 'c', 'b', 'a']);
      expect(resultArray!).toEqual(['d', 'c', 'b', 'a']);
    });
  });

  describe('数组排序节点', () => {
    it('应该正确排序字符串数组（升序）', () => {
      const node = new ArraySortNode({
        id: 'sort-test',
        type: 'array/sort',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'array') return ['c', 'a', 'd', 'b'];
        if (name === 'ascending') return true;
        if (name === 'numeric') return false;
        return true;
      };

      let resultArray: any[];
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') resultArray = value;
      };

      const result = node.execute();
      expect(result).toEqual(['a', 'b', 'c', 'd']);
      expect(resultArray!).toEqual(['a', 'b', 'c', 'd']);
    });

    it('应该正确排序数字数组（降序）', () => {
      const node = new ArraySortNode({
        id: 'sort-test',
        type: 'array/sort',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'array') return [3, 1, 4, 2];
        if (name === 'ascending') return false;
        if (name === 'numeric') return true;
        return true;
      };

      let resultArray: any[];
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') resultArray = value;
      };

      const result = node.execute();
      expect(result).toEqual([4, 3, 2, 1]);
      expect(resultArray!).toEqual([4, 3, 2, 1]);
    });
  });
});

describe('新对象操作节点测试', () => {
  describe('创建对象节点', () => {
    it('应该创建空对象', () => {
      const node = new ObjectCreateNode({
        id: 'create-test',
        type: 'object/create',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'properties') return {};
        return {};
      };

      let resultObject: any;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'object') resultObject = value;
      };

      const result = node.execute();
      expect(result).toEqual({});
      expect(resultObject!).toEqual({});
    });

    it('应该创建带初始属性的对象', () => {
      const node = new ObjectCreateNode({
        id: 'create-test',
        type: 'object/create',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'properties') return { name: 'test', value: 42 };
        return {};
      };

      let resultObject: any;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'object') resultObject = value;
      };

      const result = node.execute();
      expect(result).toEqual({ name: 'test', value: 42 });
      expect(resultObject!).toEqual({ name: 'test', value: 42 });
    });
  });

  describe('检查对象属性节点', () => {
    it('应该正确检查对象是否有属性', () => {
      const node = new ObjectHasPropertyNode({
        id: 'has-test',
        type: 'object/hasProperty',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'object') return { name: 'test', value: 42 };
        if (name === 'property') return 'name';
        return {};
      };

      let hasProperty: boolean;
      let propertyType: string;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'hasProperty') hasProperty = value;
        if (name === 'propertyType') propertyType = value;
      };

      const result = node.execute();
      expect(result).toBe(true);
      expect(hasProperty!).toBe(true);
      expect(propertyType!).toBe('string');
    });

    it('应该返回false当属性不存在时', () => {
      const node = new ObjectHasPropertyNode({
        id: 'has-test',
        type: 'object/hasProperty',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'object') return { name: 'test', value: 42 };
        if (name === 'property') return 'nonexistent';
        return {};
      };

      let hasProperty: boolean;
      let propertyType: string;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'hasProperty') hasProperty = value;
        if (name === 'propertyType') propertyType = value;
      };

      const result = node.execute();
      expect(result).toBe(false);
      expect(hasProperty!).toBe(false);
      expect(propertyType!).toBe('undefined');
    });
  });
});
