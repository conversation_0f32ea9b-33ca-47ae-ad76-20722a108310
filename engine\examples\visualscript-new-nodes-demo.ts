/**
 * 新增节点演示示例
 * 展示字符串、常量和数组操作节点的使用
 */

import { NodeRegistry } from '../src/visualscript/nodes/NodeRegistry';
import { Graph } from '../src/visualscript/graph/Graph';
import { ExecutionContext } from '../src/visualscript/execution/ExecutionContext';
import { VisualScriptEngine } from '../src/visualscript/VisualScriptEngine';
import { registerAllPresetNodes } from '../src/visualscript/presets';

// 创建演示函数
export function demonstrateNewNodes() {
  console.log('=== 新增节点演示 ===\n');

  // 初始化系统
  const registry = new NodeRegistry();
  registerAllPresetNodes(registry);
  
  const engine = new VisualScriptEngine();
  const graph = new Graph();
  const context = new ExecutionContext(engine);

  // 演示1: 字符串操作链
  console.log('1. 字符串操作演示:');
  demonstrateStringOperations(registry, graph, context);

  // 演示2: 数组操作
  console.log('\n2. 数组操作演示:');
  demonstrateArrayOperations(registry, graph, context);

  // 演示3: 常量节点
  console.log('\n3. 常量节点演示:');
  demonstrateConstantNodes(registry, graph, context);
}

/**
 * 演示字符串操作
 */
function demonstrateStringOperations(registry: NodeRegistry, graph: Graph, context: ExecutionContext) {
  try {
    // 创建字符串常量节点
    const stringConstant = registry.createNode('constant/string', {
      id: 'str-const-1',
      type: 'constant/string',
      metadata: { value: '  Hello World  ' },
      graph,
      context
    });

    // 模拟节点执行
    if (stringConstant) {
      // 模拟getInputValue方法
      stringConstant.getInputValue = (name: string) => {
        if (name === 'value') return undefined; // 使用默认值
        return undefined;
      };

      // 模拟setOutputValue方法
      let stringOutput: string = '';
      stringConstant.setOutputValue = (name: string, value: any) => {
        if (name === 'value') stringOutput = value;
      };

      // 执行字符串常量节点
      stringConstant.execute();
      console.log(`  原始字符串: "${stringOutput}"`);

      // 创建去除空格节点
      const trimNode = registry.createNode('string/trim', {
        id: 'trim-1',
        type: 'string/trim',
        metadata: {},
        graph,
        context
      });

      if (trimNode) {
        trimNode.getInputValue = (name: string) => {
          if (name === 'string') return stringOutput;
          return '';
        };

        let trimmedOutput: string = '';
        trimNode.setOutputValue = (name: string, value: any) => {
          if (name === 'result') trimmedOutput = value;
        };

        trimNode.execute();
        console.log(`  去除空格后: "${trimmedOutput}"`);

        // 创建转大写节点
        const upperNode = registry.createNode('string/toUpperCase', {
          id: 'upper-1',
          type: 'string/toUpperCase',
          metadata: {},
          graph,
          context
        });

        if (upperNode) {
          upperNode.getInputValue = (name: string) => {
            if (name === 'string') return trimmedOutput;
            return '';
          };

          let upperOutput: string = '';
          upperNode.setOutputValue = (name: string, value: any) => {
            if (name === 'result') upperOutput = value;
          };

          upperNode.execute();
          console.log(`  转大写后: "${upperOutput}"`);

          // 创建字符串替换节点
          const replaceNode = registry.createNode('string/replace', {
            id: 'replace-1',
            type: 'string/replace',
            metadata: {},
            graph,
            context
          });

          if (replaceNode) {
            replaceNode.getInputValue = (name: string) => {
              if (name === 'string') return upperOutput;
              if (name === 'searchValue') return 'WORLD';
              if (name === 'replaceValue') return 'UNIVERSE';
              if (name === 'replaceAll') return false;
              return '';
            };

            let replaceOutput: string = '';
            replaceNode.setOutputValue = (name: string, value: any) => {
              if (name === 'result') replaceOutput = value;
            };

            replaceNode.execute();
            console.log(`  替换后: "${replaceOutput}"`);

            // 创建字符串长度节点
            const lengthNode = registry.createNode('string/length', {
              id: 'length-1',
              type: 'string/length',
              metadata: {},
              graph,
              context
            });

            if (lengthNode) {
              lengthNode.getInputValue = (name: string) => {
                if (name === 'string') return replaceOutput;
                return '';
              };

              let lengthOutput: number = 0;
              lengthNode.setOutputValue = (name: string, value: any) => {
                if (name === 'length') lengthOutput = value;
              };

              lengthNode.execute();
              console.log(`  字符串长度: ${lengthOutput}`);
            }
          }
        }
      }
    }
  } catch (error) {
    console.error('字符串操作演示出错:', error);
  }
}

/**
 * 演示数组操作
 */
function demonstrateArrayOperations(registry: NodeRegistry, graph: Graph, context: ExecutionContext) {
  try {
    // 创建数组
    const createNode = registry.createNode('array/create', {
      id: 'array-create-1',
      type: 'array/create',
      metadata: {},
      graph,
      context
    });

    if (createNode) {
      createNode.getInputValue = (name: string) => {
        if (name === 'size') return 3;
        if (name === 'initialValue') return 'item';
        return 0;
      };

      let arrayOutput: any[] = [];
      let lengthOutput: number = 0;
      createNode.setOutputValue = (name: string, value: any) => {
        if (name === 'array') arrayOutput = value;
        if (name === 'length') lengthOutput = value;
      };

      createNode.execute();
      console.log(`  创建数组: [${arrayOutput.join(', ')}], 长度: ${lengthOutput}`);

      // 添加元素
      const pushNode = registry.createNode('array/push', {
        id: 'array-push-1',
        type: 'array/push',
        metadata: {},
        graph,
        context
      });

      if (pushNode) {
        pushNode.getInputValue = (name: string) => {
          if (name === 'array') return arrayOutput;
          if (name === 'element') return 'new-item';
          return null;
        };

        let pushArrayOutput: any[] = [];
        let pushLengthOutput: number = 0;
        pushNode.setOutputValue = (name: string, value: any) => {
          if (name === 'result') pushArrayOutput = value;
          if (name === 'length') pushLengthOutput = value;
        };

        pushNode.execute();
        console.log(`  添加元素后: [${pushArrayOutput.join(', ')}], 长度: ${pushLengthOutput}`);

        // 移除元素
        const popNode = registry.createNode('array/pop', {
          id: 'array-pop-1',
          type: 'array/pop',
          metadata: {},
          graph,
          context
        });

        if (popNode) {
          popNode.getInputValue = (name: string) => {
            if (name === 'array') return pushArrayOutput;
            return [];
          };

          let popArrayOutput: any[] = [];
          let poppedElement: any = null;
          let popLengthOutput: number = 0;
          popNode.setOutputValue = (name: string, value: any) => {
            if (name === 'result') popArrayOutput = value;
            if (name === 'element') poppedElement = value;
            if (name === 'length') popLengthOutput = value;
          };

          popNode.execute();
          console.log(`  移除元素后: [${popArrayOutput.join(', ')}], 移除的元素: ${poppedElement}, 长度: ${popLengthOutput}`);
        }
      }
    }
  } catch (error) {
    console.error('数组操作演示出错:', error);
  }
}

/**
 * 演示常量节点
 */
function demonstrateConstantNodes(registry: NodeRegistry, graph: Graph, context: ExecutionContext) {
  try {
    // 布尔常量
    const boolNode = registry.createNode('constant/boolean', {
      id: 'bool-const-1',
      type: 'constant/boolean',
      metadata: { value: true },
      graph,
      context
    });

    if (boolNode) {
      boolNode.getInputValue = (name: string) => undefined; // 使用默认值

      let boolOutput: boolean = false;
      boolNode.setOutputValue = (name: string, value: any) => {
        if (name === 'value') boolOutput = value;
      };

      boolNode.execute();
      console.log(`  布尔常量: ${boolOutput}`);
    }

    // 字符串常量
    const stringNode = registry.createNode('constant/string', {
      id: 'str-const-2',
      type: 'constant/string',
      metadata: { value: 'Hello from constant!' },
      graph,
      context
    });

    if (stringNode) {
      stringNode.getInputValue = (name: string) => undefined; // 使用默认值

      let stringOutput: string = '';
      stringNode.setOutputValue = (name: string, value: any) => {
        if (name === 'value') stringOutput = value;
      };

      stringNode.execute();
      console.log(`  字符串常量: "${stringOutput}"`);
    }
  } catch (error) {
    console.error('常量节点演示出错:', error);
  }
}

// 如果直接运行此文件，执行演示
if (require.main === module) {
  demonstrateNewNodes();
}
