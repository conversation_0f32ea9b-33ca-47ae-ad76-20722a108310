/**
 * 实体节点测试
 * 测试新实现的实体和组件操作节点 (166-180)
 */
import { NodeRegistry } from '../nodes/NodeRegistry';
import { NodeCategory } from '../nodes/Node';
import { registerEntityNodes } from '../presets/EntityNodes';

/**
 * 实体节点测试类
 */
export class EntityNodesTest {
  private registry: NodeRegistry;

  constructor() {
    this.registry = new NodeRegistry();
    
    // 注册实体节点
    registerEntityNodes(this.registry);
  }

  /**
   * 测试实体操作节点
   */
  public testEntityNodes(): void {
    console.log('=== 测试实体操作节点 ===');

    // 测试的实体节点类型
    const entityNodes = [
      'entity/create',
      'entity/destroy', 
      'entity/get',
      'entity/getName',
      'entity/setName',
      'entity/getTag',
      'entity/setTag',
      'entity/isActive',
      'entity/setActive',
      'entity/getParent',
      'entity/setParent',
      'entity/getChildren'
    ];

    console.log('验证实体节点注册...');
    for (const nodeType of entityNodes) {
      const nodeInfo = this.registry.getNodeTypeInfo(nodeType);
      if (!nodeInfo) {
        throw new Error(`实体节点 ${nodeType} 未正确注册`);
      }
      console.log(`✓ ${nodeType}: ${nodeInfo.label}`);
    }
  }

  /**
   * 测试组件操作节点
   */
  public testComponentNodes(): void {
    console.log('=== 测试组件操作节点 ===');

    // 测试的组件节点类型
    const componentNodes = [
      'component/add',
      'component/remove',
      'component/get'
    ];

    console.log('验证组件节点注册...');
    for (const nodeType of componentNodes) {
      const nodeInfo = this.registry.getNodeTypeInfo(nodeType);
      if (!nodeInfo) {
        throw new Error(`组件节点 ${nodeType} 未正确注册`);
      }
      console.log(`✓ ${nodeType}: ${nodeInfo.label}`);
    }
  }

  /**
   * 测试节点分类
   */
  public testNodeCategories(): void {
    console.log('=== 测试节点分类 ===');

    // 获取实体分类的节点
    const entityNodes = this.registry.getNodeTypesByCategory(NodeCategory.ENTITY);
    console.log('实体分类节点数量:', entityNodes.length);

    // 验证所有新节点都在实体分类中
    const expectedNodes = [
      'entity/create', 'entity/destroy', 'entity/get',
      'entity/getName', 'entity/setName', 'entity/getTag', 'entity/setTag',
      'entity/isActive', 'entity/setActive', 'entity/getParent', 
      'entity/setParent', 'entity/getChildren',
      'component/add', 'component/remove', 'component/get'
    ];

    for (const nodeType of expectedNodes) {
      const found = entityNodes.some(node => node.type === nodeType);
      if (!found) {
        throw new Error(`节点 ${nodeType} 未在实体分类中找到`);
      }
    }

    console.log('✓ 所有新节点都正确分类到实体类别');
  }

  /**
   * 测试节点标签
   */
  public testNodeTags(): void {
    console.log('=== 测试节点标签 ===');

    // 测试实体标签
    const entityTagNodes = this.registry.getNodeTypesByTag('entity');
    console.log('带有"entity"标签的节点数量:', entityTagNodes.length);

    // 测试组件标签
    const componentTagNodes = this.registry.getNodeTypesByTag('component');
    console.log('带有"component"标签的节点数量:', componentTagNodes.length);

    // 验证特定节点的标签
    const createEntityNode = this.registry.getNodeTypeInfo('entity/create');
    if (createEntityNode && createEntityNode.tags) {
      console.log('创建实体节点标签:', createEntityNode.tags);
      if (!createEntityNode.tags.includes('entity') || !createEntityNode.tags.includes('create')) {
        throw new Error('创建实体节点标签不正确');
      }
    }

    console.log('✓ 节点标签测试通过');
  }

  /**
   * 测试节点搜索
   */
  public testNodeSearch(): void {
    console.log('=== 测试节点搜索 ===');

    // 搜索实体相关节点
    const entitySearchResults = this.registry.searchNodeTypes('实体');
    console.log('搜索"实体"的结果数量:', entitySearchResults.length);

    // 搜索组件相关节点
    const componentSearchResults = this.registry.searchNodeTypes('组件');
    console.log('搜索"组件"的结果数量:', componentSearchResults.length);

    // 搜索创建相关节点
    const createSearchResults = this.registry.searchNodeTypes('创建');
    console.log('搜索"创建"的结果数量:', createSearchResults.length);

    console.log('✓ 节点搜索测试通过');
  }

  /**
   * 运行所有测试
   */
  public runAllTests(): void {
    console.log('开始测试新实现的实体和组件操作节点 (166-180)...');
    
    try {
      this.testEntityNodes();
      this.testComponentNodes();
      this.testNodeCategories();
      this.testNodeTags();
      this.testNodeSearch();
      
      console.log('✅ 所有测试通过！');
      console.log('新实现的15个节点已成功注册并可在编辑器中使用。');
    } catch (error) {
      console.error('❌ 测试失败:', error);
      throw error;
    }
  }
}

// 导出测试类供其他文件使用
export default EntityNodesTest;
