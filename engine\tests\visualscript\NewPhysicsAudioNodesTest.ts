/**
 * 新增物理和音频节点测试
 * 测试节点196-210的功能
 */

import { NodeRegistry } from '../../src/visualscript/nodes/NodeRegistry';
import { World } from '../../src/core/World';
import { Entity } from '../../src/core/Entity';
import { PhysicsSystem } from '../../src/physics/PhysicsSystem';
import { AudioSystem } from '../../src/audio/AudioSystem';
import { PhysicsBodyComponent } from '../../src/physics/components/PhysicsBodyComponent';
import { TransformComponent } from '../../src/core/TransformComponent';
import { Vector3 } from '../../src/math/Vector3';
import { ExecutionContext } from '../../src/visualscript/execution/ExecutionContext';
import { VisualScriptEngine } from '../../src/visualscript/VisualScriptEngine';

// 导入新节点
import {
  RaycastNode,
  ApplyForceNode,
  ApplyImpulseNode,
  SetVelocityNode,
  GetVelocityNode,
  SetMassNode,
  GetMassNode,
  OnCollisionEnterNode,
  OnCollisionExitNode,
  OnTriggerEnterNode,
  OnTriggerExitNode,
  SetGravityNode
} from '../../src/visualscript/presets/PhysicsNodes';

import {
  PlaySoundNode,
  StopSoundNode,
  PauseSoundNode,
  ResumeSoundNode
} from '../../src/visualscript/presets/AudioNodes';

/**
 * 新增节点测试类
 */
export class NewPhysicsAudioNodesTest {
  private registry: NodeRegistry;
  private world: World;
  private physicsSystem: PhysicsSystem;
  private audioSystem: AudioSystem;
  private context: ExecutionContext;

  constructor() {
    this.registry = new NodeRegistry();
    this.world = new World();
    this.physicsSystem = new PhysicsSystem();
    this.audioSystem = new AudioSystem();
    
    // 添加系统到世界
    this.world.addSystem(this.physicsSystem);
    this.world.addSystem(this.audioSystem);

    // 创建执行上下文
    const engine = new VisualScriptEngine({
      script: { nodes: [], connections: [], variables: [], customEvents: [] },
      nodeRegistry: this.registry,
      valueTypeRegistry: null as any,
      entity: new Entity('test'),
      world: this.world
    });

    this.context = new ExecutionContext({
      engine,
      entity: new Entity('test'),
      world: this.world
    });
  }

  /**
   * 测试物理节点 (196-207)
   */
  public testPhysicsNodes(): void {
    console.log('=== 测试物理节点 (196-207) ===');

    // 创建测试实体
    const entity = new Entity('testEntity');
    const transform = new TransformComponent(entity);
    const physicsBody = new PhysicsBodyComponent(entity);
    
    entity.addComponent(transform);
    entity.addComponent(physicsBody);
    this.world.addEntity(entity);

    // 测试射线检测节点 (196)
    this.testRaycastNode(entity);

    // 测试应用力节点 (197)
    this.testApplyForceNode(entity);

    // 测试应用冲量节点 (198)
    this.testApplyImpulseNode(entity);

    // 测试设置速度节点 (199)
    this.testSetVelocityNode(entity);

    // 测试获取速度节点 (200)
    this.testGetVelocityNode(entity);

    // 测试设置质量节点 (201)
    this.testSetMassNode(entity);

    // 测试获取质量节点 (202)
    this.testGetMassNode(entity);

    // 测试碰撞事件节点 (203-204)
    this.testCollisionEventNodes(entity);

    // 测试触发器事件节点 (205-206)
    this.testTriggerEventNodes(entity);

    // 测试设置重力节点 (207)
    this.testSetGravityNode();

    console.log('✓ 物理节点测试完成');
  }

  /**
   * 测试音频节点 (208-210)
   */
  public testAudioNodes(): void {
    console.log('=== 测试音频节点 (208-210) ===');

    // 测试播放音效节点 (208)
    this.testPlaySoundNode();

    // 测试停止音效节点 (209)
    this.testStopSoundNode();

    // 测试暂停音效节点 (210)
    this.testPauseSoundNode();

    console.log('✓ 音频节点测试完成');
  }

  /**
   * 测试射线检测节点
   */
  private testRaycastNode(entity: Entity): void {
    console.log('测试射线检测节点...');
    
    const node = new RaycastNode({
      id: 'raycast-test',
      type: 'physics/raycast',
      metadata: { positionX: 0, positionY: 0 },
      graph: null as any,
      context: this.context
    });

    // 设置输入值
    node.setInputValue('origin', new Vector3(0, 10, 0));
    node.setInputValue('direction', new Vector3(0, -1, 0));
    node.setInputValue('maxDistance', 20);

    // 执行节点
    const result = node.execute();
    console.log('射线检测结果:', result);
  }

  /**
   * 测试应用力节点
   */
  private testApplyForceNode(entity: Entity): void {
    console.log('测试应用力节点...');
    
    const node = new ApplyForceNode({
      id: 'apply-force-test',
      type: 'physics/applyForce',
      metadata: { positionX: 0, positionY: 0 },
      graph: null as any,
      context: this.context
    });

    // 设置输入值
    node.setInputValue('entity', entity);
    node.setInputValue('force', new Vector3(0, 100, 0));

    // 执行节点
    const result = node.execute();
    console.log('应用力结果:', result);
  }

  /**
   * 测试应用冲量节点
   */
  private testApplyImpulseNode(entity: Entity): void {
    console.log('测试应用冲量节点...');
    
    const node = new ApplyImpulseNode({
      id: 'apply-impulse-test',
      type: 'physics/applyImpulse',
      metadata: { positionX: 0, positionY: 0 },
      graph: null as any,
      context: this.context
    });

    // 设置输入值
    node.setInputValue('entity', entity);
    node.setInputValue('impulse', new Vector3(0, 50, 0));

    // 执行节点
    const result = node.execute();
    console.log('应用冲量结果:', result);
  }

  /**
   * 测试设置速度节点
   */
  private testSetVelocityNode(entity: Entity): void {
    console.log('测试设置速度节点...');
    
    const node = new SetVelocityNode({
      id: 'set-velocity-test',
      type: 'physics/setVelocity',
      metadata: { positionX: 0, positionY: 0 },
      graph: null as any,
      context: this.context
    });

    // 设置输入值
    node.setInputValue('entity', entity);
    node.setInputValue('velocity', new Vector3(5, 0, 0));

    // 执行节点
    const result = node.execute();
    console.log('设置速度结果:', result);
  }

  /**
   * 测试获取速度节点
   */
  private testGetVelocityNode(entity: Entity): void {
    console.log('测试获取速度节点...');
    
    const node = new GetVelocityNode({
      id: 'get-velocity-test',
      type: 'physics/getVelocity',
      metadata: { positionX: 0, positionY: 0 },
      graph: null as any,
      context: this.context
    });

    // 设置输入值
    node.setInputValue('entity', entity);

    // 执行节点
    const result = node.execute();
    console.log('获取速度结果:', result);
  }

  /**
   * 测试设置质量节点
   */
  private testSetMassNode(entity: Entity): void {
    console.log('测试设置质量节点...');
    
    const node = new SetMassNode({
      id: 'set-mass-test',
      type: 'physics/setMass',
      metadata: { positionX: 0, positionY: 0 },
      graph: null as any,
      context: this.context
    });

    // 设置输入值
    node.setInputValue('entity', entity);
    node.setInputValue('mass', 2.5);

    // 执行节点
    const result = node.execute();
    console.log('设置质量结果:', result);
  }

  /**
   * 测试获取质量节点
   */
  private testGetMassNode(entity: Entity): void {
    console.log('测试获取质量节点...');
    
    const node = new GetMassNode({
      id: 'get-mass-test',
      type: 'physics/getMass',
      metadata: { positionX: 0, positionY: 0 },
      graph: null as any,
      context: this.context
    });

    // 设置输入值
    node.setInputValue('entity', entity);

    // 执行节点
    const result = node.execute();
    console.log('获取质量结果:', result);
  }

  /**
   * 测试碰撞事件节点
   */
  private testCollisionEventNodes(entity: Entity): void {
    console.log('测试碰撞事件节点...');
    
    // 测试碰撞开始事件
    const enterNode = new OnCollisionEnterNode({
      id: 'collision-enter-test',
      type: 'physics/onCollisionEnter',
      metadata: { positionX: 0, positionY: 0 },
      graph: null as any,
      context: this.context
    });

    enterNode.setInputValue('entity', entity);
    enterNode.initialize();

    // 测试碰撞结束事件
    const exitNode = new OnCollisionExitNode({
      id: 'collision-exit-test',
      type: 'physics/onCollisionExit',
      metadata: { positionX: 0, positionY: 0 },
      graph: null as any,
      context: this.context
    });

    exitNode.setInputValue('entity', entity);
    exitNode.initialize();

    console.log('碰撞事件节点初始化完成');
  }

  /**
   * 测试触发器事件节点
   */
  private testTriggerEventNodes(entity: Entity): void {
    console.log('测试触发器事件节点...');
    
    // 测试触发器进入事件
    const enterNode = new OnTriggerEnterNode({
      id: 'trigger-enter-test',
      type: 'physics/onTriggerEnter',
      metadata: { positionX: 0, positionY: 0 },
      graph: null as any,
      context: this.context
    });

    enterNode.setInputValue('entity', entity);
    enterNode.initialize();

    // 测试触发器退出事件
    const exitNode = new OnTriggerExitNode({
      id: 'trigger-exit-test',
      type: 'physics/onTriggerExit',
      metadata: { positionX: 0, positionY: 0 },
      graph: null as any,
      context: this.context
    });

    exitNode.setInputValue('entity', entity);
    exitNode.initialize();

    console.log('触发器事件节点初始化完成');
  }

  /**
   * 测试设置重力节点
   */
  private testSetGravityNode(): void {
    console.log('测试设置重力节点...');
    
    const node = new SetGravityNode({
      id: 'set-gravity-test',
      type: 'physics/setGravity',
      metadata: { positionX: 0, positionY: 0 },
      graph: null as any,
      context: this.context
    });

    // 设置输入值
    node.setInputValue('gravity', new Vector3(0, -20, 0));

    // 执行节点
    const result = node.execute();
    console.log('设置重力结果:', result);
  }

  /**
   * 测试播放音效节点
   */
  private testPlaySoundNode(): void {
    console.log('测试播放音效节点...');
    
    const node = new PlaySoundNode({
      id: 'play-sound-test',
      type: 'audio/playSound',
      metadata: { positionX: 0, positionY: 0 },
      graph: null as any,
      context: this.context
    });

    // 设置输入值
    node.setInputValue('audioUrl', 'test-sound.mp3');
    node.setInputValue('volume', 0.8);
    node.setInputValue('loop', false);

    // 执行节点
    const result = node.execute();
    console.log('播放音效结果:', result);
  }

  /**
   * 测试停止音效节点
   */
  private testStopSoundNode(): void {
    console.log('测试停止音效节点...');
    
    const node = new StopSoundNode({
      id: 'stop-sound-test',
      type: 'audio/stopSound',
      metadata: { positionX: 0, positionY: 0 },
      graph: null as any,
      context: this.context
    });

    // 设置输入值
    node.setInputValue('audioId', 'test-audio-id');

    // 执行节点
    const result = node.execute();
    console.log('停止音效结果:', result);
  }

  /**
   * 测试暂停音效节点
   */
  private testPauseSoundNode(): void {
    console.log('测试暂停音效节点...');
    
    const node = new PauseSoundNode({
      id: 'pause-sound-test',
      type: 'audio/pauseSound',
      metadata: { positionX: 0, positionY: 0 },
      graph: null as any,
      context: this.context
    });

    // 设置输入值
    node.setInputValue('audioId', 'test-audio-id');

    // 执行节点
    const result = node.execute();
    console.log('暂停音效结果:', result);
  }

  /**
   * 运行所有测试
   */
  public runAllTests(): void {
    console.log('=== 开始测试新增物理和音频节点 ===\n');

    try {
      this.testPhysicsNodes();
      this.testAudioNodes();
      
      console.log('\n=== 所有测试完成 ===');
      console.log('✓ 物理节点 (196-207) 测试通过');
      console.log('✓ 音频节点 (208-210) 测试通过');
    } catch (error) {
      console.error('测试失败:', error);
    }
  }
}

// 导出测试函数
export function runNewNodesTest(): void {
  const test = new NewPhysicsAudioNodesTest();
  test.runAllTests();
}
