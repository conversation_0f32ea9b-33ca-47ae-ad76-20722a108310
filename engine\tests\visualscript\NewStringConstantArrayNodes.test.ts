/**
 * 新字符串、常量和数组节点单元测试
 * 测试新实现的字符串操作、常量和数组操作节点
 */
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { NodeRegistry } from '../../src/visualscript/nodes/NodeRegistry';
import {
  StringConstantNode,
  BooleanConstantNode
} from '../../src/visualscript/presets/ConstantNodes';
import {
  StringSubstringNode,
  StringIndexOfNode,
  StringReplaceNode,
  StringToUpperCaseNode,
  StringToLowerCaseNode,
  StringTrimNode,
  StringLengthNode,
  StringContainsNode
} from '../../src/visualscript/presets/StringNodes';
import {
  ArrayCreateNode
} from '../../src/visualscript/presets/ArrayNodes';

describe('新常量节点测试', () => {
  let registry: NodeRegistry;

  beforeEach(() => {
    registry = new NodeRegistry();
  });

  afterEach(() => {
    registry.clear();
  });

  describe('字符串常量节点', () => {
    it('应该正确输出字符串常量', () => {
      const node = new StringConstantNode({
        id: 'string-const-test',
        type: 'constant/string',
        metadata: { value: 'Hello World' },
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'value') return 'Hello World';
        return undefined;
      };

      let outputValue: string;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'value') outputValue = value;
      };

      const result = node.execute();
      expect(result).toBe('Hello World');
      expect(outputValue!).toBe('Hello World');
    });

    it('应该使用默认值当没有输入时', () => {
      const node = new StringConstantNode({
        id: 'string-const-test',
        type: 'constant/string',
        metadata: { value: 'Default Text' },
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => undefined;

      let outputValue: string;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'value') outputValue = value;
      };

      const result = node.execute();
      expect(result).toBe('Default Text');
      expect(outputValue!).toBe('Default Text');
    });
  });

  describe('布尔常量节点', () => {
    it('应该正确输出布尔常量', () => {
      const node = new BooleanConstantNode({
        id: 'bool-const-test',
        type: 'constant/boolean',
        metadata: { value: true },
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'value') return true;
        return undefined;
      };

      let outputValue: boolean;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'value') outputValue = value;
      };

      const result = node.execute();
      expect(result).toBe(true);
      expect(outputValue!).toBe(true);
    });

    it('应该使用默认值当没有输入时', () => {
      const node = new BooleanConstantNode({
        id: 'bool-const-test',
        type: 'constant/boolean',
        metadata: { value: false },
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => undefined;

      let outputValue: boolean;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'value') outputValue = value;
      };

      const result = node.execute();
      expect(result).toBe(false);
      expect(outputValue!).toBe(false);
    });
  });
});

describe('新字符串操作节点测试', () => {
  describe('子字符串节点', () => {
    it('应该正确提取子字符串', () => {
      const node = new StringSubstringNode({
        id: 'substring-test',
        type: 'string/substring',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'string') return 'Hello World';
        if (name === 'start') return 6;
        if (name === 'length') return 5;
        return 0;
      };

      let outputValue: string;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') outputValue = value;
      };

      const result = node.execute();
      expect(result).toBe('World');
      expect(outputValue!).toBe('World');
    });

    it('应该正确提取从指定位置到末尾的子字符串', () => {
      const node = new StringSubstringNode({
        id: 'substring-test',
        type: 'string/substring',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'string') return 'Hello World';
        if (name === 'start') return 6;
        if (name === 'length') return -1;
        return 0;
      };

      let outputValue: string;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') outputValue = value;
      };

      const result = node.execute();
      expect(result).toBe('World');
      expect(outputValue!).toBe('World');
    });
  });

  describe('查找位置节点', () => {
    it('应该正确查找子字符串位置', () => {
      const node = new StringIndexOfNode({
        id: 'indexOf-test',
        type: 'string/indexOf',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'string') return 'Hello World';
        if (name === 'searchString') return 'World';
        if (name === 'startIndex') return 0;
        return 0;
      };

      let indexValue: number;
      let foundValue: boolean;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'index') indexValue = value;
        if (name === 'found') foundValue = value;
      };

      const result = node.execute();
      expect(indexValue!).toBe(6);
      expect(foundValue!).toBe(true);
    });

    it('应该返回-1当未找到子字符串时', () => {
      const node = new StringIndexOfNode({
        id: 'indexOf-test',
        type: 'string/indexOf',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'string') return 'Hello World';
        if (name === 'searchString') return 'xyz';
        if (name === 'startIndex') return 0;
        return 0;
      };

      let indexValue: number;
      let foundValue: boolean;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'index') indexValue = value;
        if (name === 'found') foundValue = value;
      };

      const result = node.execute();
      expect(indexValue!).toBe(-1);
      expect(foundValue!).toBe(false);
    });
  });

  describe('字符串替换节点', () => {
    it('应该正确替换字符串', () => {
      const node = new StringReplaceNode({
        id: 'replace-test',
        type: 'string/replace',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'string') return 'Hello World';
        if (name === 'searchValue') return 'World';
        if (name === 'replaceValue') return 'Universe';
        if (name === 'replaceAll') return false;
        return '';
      };

      let outputValue: string;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') outputValue = value;
      };

      const result = node.execute();
      expect(result).toBe('Hello Universe');
      expect(outputValue!).toBe('Hello Universe');
    });

    it('应该正确替换所有匹配项', () => {
      const node = new StringReplaceNode({
        id: 'replace-test',
        type: 'string/replace',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'string') return 'Hello Hello Hello';
        if (name === 'searchValue') return 'Hello';
        if (name === 'replaceValue') return 'Hi';
        if (name === 'replaceAll') return true;
        return '';
      };

      let outputValue: string;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') outputValue = value;
      };

      const result = node.execute();
      expect(result).toBe('Hi Hi Hi');
      expect(outputValue!).toBe('Hi Hi Hi');
    });
  });

  describe('转大写节点', () => {
    it('应该正确转换为大写', () => {
      const node = new StringToUpperCaseNode({
        id: 'uppercase-test',
        type: 'string/toUpperCase',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'string') return 'hello world';
        return '';
      };

      let outputValue: string;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') outputValue = value;
      };

      const result = node.execute();
      expect(result).toBe('HELLO WORLD');
      expect(outputValue!).toBe('HELLO WORLD');
    });
  });

  describe('转小写节点', () => {
    it('应该正确转换为小写', () => {
      const node = new StringToLowerCaseNode({
        id: 'lowercase-test',
        type: 'string/toLowerCase',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'string') return 'HELLO WORLD';
        return '';
      };

      let outputValue: string;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') outputValue = value;
      };

      const result = node.execute();
      expect(result).toBe('hello world');
      expect(outputValue!).toBe('hello world');
    });
  });

  describe('去除空格节点', () => {
    it('应该正确去除首尾空格', () => {
      const node = new StringTrimNode({
        id: 'trim-test',
        type: 'string/trim',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'string') return '  hello world  ';
        return '';
      };

      let outputValue: string;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'result') outputValue = value;
      };

      const result = node.execute();
      expect(result).toBe('hello world');
      expect(outputValue!).toBe('hello world');
    });
  });

  describe('字符串长度节点', () => {
    it('应该正确获取字符串长度', () => {
      const node = new StringLengthNode({
        id: 'length-test',
        type: 'string/length',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'string') return 'hello world';
        return '';
      };

      let outputValue: number;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'length') outputValue = value;
      };

      const result = node.execute();
      expect(result).toBe(11);
      expect(outputValue!).toBe(11);
    });
  });

  describe('包含检查节点', () => {
    it('应该正确检查字符串是否包含子串', () => {
      const node = new StringContainsNode({
        id: 'contains-test',
        type: 'string/contains',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'string') return 'hello world';
        if (name === 'searchString') return 'world';
        return '';
      };

      let outputValue: boolean;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'contains') outputValue = value;
      };

      const result = node.execute();
      expect(result).toBe(true);
      expect(outputValue!).toBe(true);
    });

    it('应该返回false当不包含子串时', () => {
      const node = new StringContainsNode({
        id: 'contains-test',
        type: 'string/contains',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'string') return 'hello world';
        if (name === 'searchString') return 'xyz';
        return '';
      };

      let outputValue: boolean;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'contains') outputValue = value;
      };

      const result = node.execute();
      expect(result).toBe(false);
      expect(outputValue!).toBe(false);
    });
  });
});

describe('新数组操作节点测试', () => {
  describe('数组创建节点', () => {
    it('应该创建空数组', () => {
      const node = new ArrayCreateNode({
        id: 'array-create-test',
        type: 'array/create',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'size') return 0;
        if (name === 'initialValue') return null;
        return 0;
      };

      let arrayValue: any[];
      let lengthValue: number;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'array') arrayValue = value;
        if (name === 'length') lengthValue = value;
      };

      const result = node.execute();
      expect(result).toEqual([]);
      expect(arrayValue!).toEqual([]);
      expect(lengthValue!).toBe(0);
    });

    it('应该创建指定大小的数组', () => {
      const node = new ArrayCreateNode({
        id: 'array-create-test',
        type: 'array/create',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'size') return 3;
        if (name === 'initialValue') return 'default';
        return 0;
      };

      let arrayValue: any[];
      let lengthValue: number;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'array') arrayValue = value;
        if (name === 'length') lengthValue = value;
      };

      const result = node.execute();
      expect(result).toEqual(['default', 'default', 'default']);
      expect(arrayValue!).toEqual(['default', 'default', 'default']);
      expect(lengthValue!).toBe(3);
    });

    it('应该创建指定大小的数组且使用null作为初始值', () => {
      const node = new ArrayCreateNode({
        id: 'array-create-test',
        type: 'array/create',
        metadata: {},
        graph: null,
        context: null
      });

      node.getInputValue = (name: string) => {
        if (name === 'size') return 2;
        if (name === 'initialValue') return null;
        return 0;
      };

      let arrayValue: any[];
      let lengthValue: number;
      node.setOutputValue = (name: string, value: any) => {
        if (name === 'array') arrayValue = value;
        if (name === 'length') lengthValue = value;
      };

      const result = node.execute();
      expect(result).toEqual([null, null]);
      expect(arrayValue!).toEqual([null, null]);
      expect(lengthValue!).toBe(2);
    });
  });
});
