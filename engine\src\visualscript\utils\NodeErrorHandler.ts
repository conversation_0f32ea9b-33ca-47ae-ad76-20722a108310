/**
 * 视觉脚本节点错误处理工具
 * 提供统一的错误处理和日志记录功能
 */

export enum ErrorLevel {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  CRITICAL = 'critical'
}

export interface NodeError {
  nodeId: string;
  nodeType: string;
  level: ErrorLevel;
  message: string;
  details?: any;
  timestamp: number;
  stack?: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * 节点错误处理器
 */
export class NodeErrorHandler {
  private static instance: NodeErrorHandler;
  private errors: NodeError[] = [];
  private maxErrors: number = 1000;
  private listeners: ((error: NodeError) => void)[] = [];

  private constructor() {}

  public static getInstance(): NodeErrorHandler {
    if (!NodeErrorHandler.instance) {
      NodeErrorHandler.instance = new NodeErrorHandler();
    }
    return NodeErrorHandler.instance;
  }

  /**
   * 记录错误
   * @param nodeId 节点ID
   * @param nodeType 节点类型
   * @param level 错误级别
   * @param message 错误消息
   * @param details 错误详情
   * @param error 原始错误对象
   */
  public logError(
    nodeId: string,
    nodeType: string,
    level: ErrorLevel,
    message: string,
    details?: any,
    error?: Error
  ): void {
    const nodeError: NodeError = {
      nodeId,
      nodeType,
      level,
      message,
      details,
      timestamp: Date.now(),
      stack: error?.stack
    };

    this.errors.push(nodeError);

    // 限制错误数量
    if (this.errors.length > this.maxErrors) {
      this.errors.shift();
    }

    // 通知监听器
    this.listeners.forEach(listener => listener(nodeError));

    // 根据错误级别输出到控制台
    this.outputToConsole(nodeError);
  }

  /**
   * 输出错误到控制台
   * @param nodeError 节点错误
   */
  private outputToConsole(nodeError: NodeError): void {
    const prefix = `[${nodeError.nodeType}:${nodeError.nodeId}]`;
    const message = `${prefix} ${nodeError.message}`;

    switch (nodeError.level) {
      case ErrorLevel.INFO:
        console.info(message, nodeError.details);
        break;
      case ErrorLevel.WARNING:
        console.warn(message, nodeError.details);
        break;
      case ErrorLevel.ERROR:
        console.error(message, nodeError.details);
        if (nodeError.stack) {
          console.error(nodeError.stack);
        }
        break;
      case ErrorLevel.CRITICAL:
        console.error(`🚨 CRITICAL: ${message}`, nodeError.details);
        if (nodeError.stack) {
          console.error(nodeError.stack);
        }
        break;
    }
  }

  /**
   * 添加错误监听器
   * @param listener 监听器函数
   */
  public addErrorListener(listener: (error: NodeError) => void): void {
    this.listeners.push(listener);
  }

  /**
   * 移除错误监听器
   * @param listener 监听器函数
   */
  public removeErrorListener(listener: (error: NodeError) => void): void {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  /**
   * 获取所有错误
   * @returns 错误列表
   */
  public getErrors(): NodeError[] {
    return [...this.errors];
  }

  /**
   * 获取指定节点的错误
   * @param nodeId 节点ID
   * @returns 错误列表
   */
  public getNodeErrors(nodeId: string): NodeError[] {
    return this.errors.filter(error => error.nodeId === nodeId);
  }

  /**
   * 获取指定级别的错误
   * @param level 错误级别
   * @returns 错误列表
   */
  public getErrorsByLevel(level: ErrorLevel): NodeError[] {
    return this.errors.filter(error => error.level === level);
  }

  /**
   * 清除所有错误
   */
  public clearErrors(): void {
    this.errors = [];
  }

  /**
   * 清除指定节点的错误
   * @param nodeId 节点ID
   */
  public clearNodeErrors(nodeId: string): void {
    this.errors = this.errors.filter(error => error.nodeId !== nodeId);
  }

  /**
   * 验证输入值
   * @param value 输入值
   * @param expectedType 期望类型
   * @param required 是否必需
   * @param name 参数名称
   * @returns 验证结果
   */
  public static validateInput(
    value: any,
    expectedType: string,
    required: boolean = true,
    name: string = 'input'
  ): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: []
    };

    // 检查必需参数
    if (required && (value === null || value === undefined)) {
      result.isValid = false;
      result.errors.push(`${name} 是必需的参数`);
      return result;
    }

    // 如果不是必需参数且为空，则跳过类型检查
    if (!required && (value === null || value === undefined)) {
      return result;
    }

    // 类型检查
    const actualType = typeof value;
    switch (expectedType.toLowerCase()) {
      case 'string':
        if (actualType !== 'string') {
          result.isValid = false;
          result.errors.push(`${name} 应该是字符串类型，实际是 ${actualType}`);
        }
        break;
      case 'number':
        if (actualType !== 'number' || isNaN(value)) {
          result.isValid = false;
          result.errors.push(`${name} 应该是有效的数字类型，实际是 ${actualType}`);
        }
        break;
      case 'boolean':
        if (actualType !== 'boolean') {
          result.isValid = false;
          result.errors.push(`${name} 应该是布尔类型，实际是 ${actualType}`);
        }
        break;
      case 'object':
        if (actualType !== 'object' || value === null) {
          result.isValid = false;
          result.errors.push(`${name} 应该是对象类型，实际是 ${actualType}`);
        }
        break;
      case 'array':
        if (!Array.isArray(value)) {
          result.isValid = false;
          result.errors.push(`${name} 应该是数组类型`);
        }
        break;
      case 'function':
        if (actualType !== 'function') {
          result.isValid = false;
          result.errors.push(`${name} 应该是函数类型，实际是 ${actualType}`);
        }
        break;
    }

    return result;
  }

  /**
   * 验证HTML元素
   * @param element 元素
   * @param expectedTagName 期望的标签名
   * @param name 参数名称
   * @returns 验证结果
   */
  public static validateHTMLElement(
    element: any,
    expectedTagName?: string,
    name: string = 'element'
  ): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: []
    };

    if (!(element instanceof HTMLElement)) {
      result.isValid = false;
      result.errors.push(`${name} 应该是HTML元素`);
      return result;
    }

    if (expectedTagName && element.tagName.toLowerCase() !== expectedTagName.toLowerCase()) {
      result.isValid = false;
      result.errors.push(`${name} 应该是 ${expectedTagName} 元素，实际是 ${element.tagName}`);
    }

    return result;
  }

  /**
   * 安全执行函数
   * @param nodeId 节点ID
   * @param nodeType 节点类型
   * @param fn 要执行的函数
   * @param defaultValue 默认返回值
   * @returns 执行结果
   */
  public static safeExecute<T>(
    nodeId: string,
    nodeType: string,
    fn: () => T,
    defaultValue: T
  ): T {
    try {
      return fn();
    } catch (error) {
      const errorHandler = NodeErrorHandler.getInstance();
      errorHandler.logError(
        nodeId,
        nodeType,
        ErrorLevel.ERROR,
        '节点执行失败',
        { defaultValue },
        error as Error
      );
      return defaultValue;
    }
  }
}

/**
 * 节点错误处理装饰器
 * @param target 目标对象
 * @param propertyName 属性名
 * @param descriptor 属性描述符
 */
export function handleNodeErrors(target: any, propertyName: string, descriptor: PropertyDescriptor) {
  const method = descriptor.value;

  descriptor.value = function (...args: any[]) {
    try {
      return method.apply(this, args);
    } catch (error) {
      const nodeId = this.id || 'unknown';
      const nodeType = this.type || 'unknown';

      const errorHandler = NodeErrorHandler.getInstance();
      errorHandler.logError(
        nodeId,
        nodeType,
        ErrorLevel.ERROR,
        `方法 ${propertyName} 执行失败`,
        { args },
        error as Error
      );

      return false; // 默认返回false表示失败
    }
  };
}
