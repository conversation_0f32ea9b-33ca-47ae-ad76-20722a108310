/**
 * 视觉脚本物理节点
 * 提供物理系统相关的节点
 */
import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { FunctionNode } from '../nodes/FunctionNode';
import { FlowNode } from '../nodes/FlowNode';
import { EventNode } from '../nodes/EventNode';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { Vector3 } from '../../math/Vector3';
import { PhysicsSystem } from '../../physics/PhysicsSystem';
import { PhysicsBodyComponent } from '../../physics/components/PhysicsBodyComponent';

/**
 * 射线检测节点
 * 执行物理射线检测
 */
export class RaycastNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入插槽
    this.addInput({
      name: 'origin',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '射线起点'
    });

    this.addInput({
      name: 'direction',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '射线方向'
    });

    this.addInput({
      name: 'maxDistance',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '最大检测距离',
      defaultValue: 100
    });

    this.addInput({
      name: 'collisionGroup',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '碰撞组',
      defaultValue: -1
    });

    this.addInput({
      name: 'collisionMask',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '碰撞掩码',
      defaultValue: -1
    });

    // 添加输出插槽
    this.addOutput({
      name: 'hit',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否命中'
    });

    this.addOutput({
      name: 'hitEntity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.OUTPUT,
      description: '命中的实体'
    });

    this.addOutput({
      name: 'hitPoint',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.OUTPUT,
      description: '命中点'
    });

    this.addOutput({
      name: 'hitNormal',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.OUTPUT,
      description: '命中法线'
    });

    this.addOutput({
      name: 'hitDistance',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '命中距离'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const origin = this.getInputValue('origin') as Vector3;
    const direction = this.getInputValue('direction') as Vector3;
    const maxDistance = this.getInputValue('maxDistance') as number;
    const collisionGroup = this.getInputValue('collisionGroup') as number;
    const collisionMask = this.getInputValue('collisionMask') as number;

    // 检查输入值是否有效
    if (!origin || !direction) {
      this.setOutputValue('hit', false);
      return false;
    }

    // 获取物理系统
    const physicsSystem = this.context.world.getSystem(PhysicsSystem);
    if (!physicsSystem) {
      this.setOutputValue('hit', false);
      return false;
    }

    // 计算射线终点
    const to = {
      x: origin.x + direction.x * maxDistance,
      y: origin.y + direction.y * maxDistance,
      z: origin.z + direction.z * maxDistance
    };

    // 执行射线检测
    const result = physicsSystem.raycastClosest(origin, to, {
      collisionFilterGroup: collisionGroup,
      collisionFilterMask: collisionMask
    });

    // 设置输出值
    this.setOutputValue('hit', result.hasHit());
    this.setOutputValue('hitEntity', result.getHitEntity());
    this.setOutputValue('hitPoint', result.getHitPoint());
    this.setOutputValue('hitNormal', result.getHitNormal());
    this.setOutputValue('hitDistance', result.getHitDistance());

    return result.hasHit();
  }
}

/**
 * 应用力节点
 * 向物理体应用力
 */
export class ApplyForceNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    this.addInput({
      name: 'force',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '力向量'
    });

    this.addInput({
      name: 'localPoint',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '局部应用点',
      defaultValue: new Vector3(0, 0, 0)
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const force = this.getInputValue('force') as Vector3;
    const localPoint = this.getInputValue('localPoint') as Vector3;

    // 检查输入值是否有效
    if (!entity || !force) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    // 检查实体是否有物理体组件
    if (!entity.hasComponent(PhysicsBodyComponent.type)) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    // 获取物理体组件
    const physicsBody = entity.getComponent(PhysicsBodyComponent.type) as PhysicsBodyComponent;

    // 应用力
    try {
      // 转换为THREE.Vector3
      const threeForce = new THREE.Vector3(force.x, force.y, force.z);
      const threeLocalPoint = localPoint ? new THREE.Vector3(localPoint.x, localPoint.y, localPoint.z) : undefined;

      physicsBody.applyForce(threeForce, threeLocalPoint);

      // 设置输出值
      this.setOutputValue('success', true);

      // 触发输出流程
      this.triggerFlow('flow');

      return true;
    } catch (error) {
      console.error('应用力失败:', error);

      // 设置输出值
      this.setOutputValue('success', false);

      // 触发输出流程
      this.triggerFlow('flow');

      return false;
    }
  }
}

/**
 * 碰撞检测节点
 * 检测两个实体之间的碰撞
 */
export class CollisionDetectionNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入插槽
    this.addInput({
      name: 'entityA',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '实体A'
    });

    this.addInput({
      name: 'entityB',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '实体B'
    });

    // 添加输出插槽
    this.addOutput({
      name: 'colliding',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否碰撞'
    });

    this.addOutput({
      name: 'contactPoint',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.OUTPUT,
      description: '接触点'
    });

    this.addOutput({
      name: 'contactNormal',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.OUTPUT,
      description: '接触法线'
    });

    this.addOutput({
      name: 'penetrationDepth',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '穿透深度'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entityA = this.getInputValue('entityA') as Entity;
    const entityB = this.getInputValue('entityB') as Entity;

    // 检查输入值是否有效
    if (!entityA || !entityB) {
      this.setOutputValue('colliding', false);
      return false;
    }

    // 获取物理系统
    const physicsSystem = this.context.world.getSystem(PhysicsSystem);
    if (!physicsSystem) {
      this.setOutputValue('colliding', false);
      return false;
    }

    // 模拟碰撞检测（实际应用中应该使用物理引擎的碰撞检测）
    // 获取两个实体的物理体
    const bodyA = physicsSystem.getPhysicsBody(entityA);
    const bodyB = physicsSystem.getPhysicsBody(entityB);

    if (!bodyA || !bodyB) {
      this.setOutputValue('colliding', false);
      this.setOutputValue('contactPoint', null);
      this.setOutputValue('contactNormal', null);
      this.setOutputValue('penetrationDepth', 0);
      return false;
    }

    // 简单的距离检测（实际应用中应该使用更精确的碰撞检测）
    const posA = bodyA.getPosition();
    const posB = bodyB.getPosition();

    // 转换为THREE.Vector3进行计算
    const threeA = new THREE.Vector3(posA.x, posA.y, posA.z);
    const threeB = new THREE.Vector3(posB.x, posB.y, posB.z);
    const distance = threeA.distanceTo(threeB);
    const colliding = distance < 2.0; // 简单的阈值检测

    // 设置输出值
    this.setOutputValue('colliding', colliding);
    if (colliding) {
      // 计算接触点和法线
      const contactPoint = threeA.clone().lerp(threeB, 0.5);
      const contactNormal = threeB.clone().sub(threeA).normalize();
      this.setOutputValue('contactPoint', contactPoint);
      this.setOutputValue('contactNormal', contactNormal);
      this.setOutputValue('penetrationDepth', 2.0 - distance);
    } else {
      this.setOutputValue('contactPoint', null);
      this.setOutputValue('contactNormal', null);
      this.setOutputValue('penetrationDepth', 0);
    }

    return colliding;
  }
}

/**
 * 物理约束节点
 * 创建物理约束
 */
export class CreateConstraintNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'entityA',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '实体A'
    });

    this.addInput({
      name: 'entityB',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '实体B'
    });

    this.addInput({
      name: 'constraintType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '约束类型',
      defaultValue: 'hinge'
    });

    this.addInput({
      name: 'pivotA',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '实体A上的枢轴点',
      defaultValue: new Vector3(0, 0, 0)
    });

    this.addInput({
      name: 'pivotB',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '实体B上的枢轴点',
      defaultValue: new Vector3(0, 0, 0)
    });

    this.addInput({
      name: 'axisA',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '实体A上的轴',
      defaultValue: new Vector3(0, 1, 0)
    });

    this.addInput({
      name: 'axisB',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '实体B上的轴',
      defaultValue: new Vector3(0, 1, 0)
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    this.addOutput({
      name: 'constraintId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '约束ID'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entityA = this.getInputValue('entityA') as Entity;
    const entityB = this.getInputValue('entityB') as Entity;
    const constraintType = this.getInputValue('constraintType') as string;
    const pivotA = this.getInputValue('pivotA') as Vector3;
    const pivotB = this.getInputValue('pivotB') as Vector3;
    const axisA = this.getInputValue('axisA') as Vector3;
    const axisB = this.getInputValue('axisB') as Vector3;

    // 检查输入值是否有效
    if (!entityA || !entityB) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    // 获取物理系统
    const physicsSystem = this.context.world.getSystem(PhysicsSystem);
    if (!physicsSystem) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    // 模拟约束创建（实际应用中应该使用物理引擎的约束系统）
    try {
      // 获取两个实体的物理体
      const bodyA = physicsSystem.getPhysicsBody(entityA);
      const bodyB = physicsSystem.getPhysicsBody(entityB);

      if (!bodyA || !bodyB) {
        this.setOutputValue('constraintId', '');
        this.setOutputValue('success', false);
        this.triggerFlow('flow');
        return false;
      }

      // 生成约束ID
      const constraintId = `constraint_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // 这里应该创建实际的物理约束，但由于PhysicsSystem没有相应方法，
      // 我们只是模拟成功创建
      console.log(`创建${constraintType}约束: ${constraintId}`, {
        entityA: entityA.id,
        entityB: entityB.id,
        pivotA,
        pivotB,
        axisA,
        axisB
      });

      // 设置输出值
      this.setOutputValue('constraintId', constraintId);
      this.setOutputValue('success', true);

      // 触发输出流程
      this.triggerFlow('flow');

      return true;
    } catch (error) {
      console.error('创建约束失败:', error);

      this.setOutputValue('constraintId', '');
      this.setOutputValue('success', false);
      this.triggerFlow('flow');

      return false;
    }
  }
}

/**
 * 物理材质节点
 * 创建物理材质
 */
export class CreatePhysicsMaterialNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入插槽
    this.addInput({
      name: 'friction',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '摩擦系数',
      defaultValue: 0.3
    });

    this.addInput({
      name: 'restitution',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '恢复系数',
      defaultValue: 0.3
    });

    // 添加输出插槽
    this.addOutput({
      name: 'material',
      type: SocketType.DATA,
      dataType: 'PhysicsMaterial',
      direction: SocketDirection.OUTPUT,
      description: '物理材质'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const friction = this.getInputValue('friction') as number;
    const restitution = this.getInputValue('restitution') as number;

    // 获取物理系统
    const physicsSystem = this.context.world.getSystem(PhysicsSystem);
    if (!physicsSystem) {
      return null;
    }

    // 创建物理材质（使用CANNON.js的Material）
    try {
      // 直接使用CANNON.js创建材质
      const material = new (physicsSystem.getPhysicsWorld().constructor as any).Material({
        friction,
        restitution
      });

      // 设置输出值
      this.setOutputValue('material', material);

      return material;
    } catch (error) {
      console.error('创建物理材质失败:', error);

      // 创建一个简单的材质对象作为备用
      const fallbackMaterial = {
        friction,
        restitution,
        type: 'PhysicsMaterial'
      };

      this.setOutputValue('material', fallbackMaterial);
      return fallbackMaterial;
    }
  }
}

/**
 * 应用冲量节点
 * 向物理体应用瞬时冲量
 */
export class ApplyImpulseNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    this.addInput({
      name: 'impulse',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '冲量向量'
    });

    this.addInput({
      name: 'localPoint',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '局部应用点',
      defaultValue: new Vector3(0, 0, 0)
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const impulse = this.getInputValue('impulse') as Vector3;
    const localPoint = this.getInputValue('localPoint') as Vector3;

    // 检查输入值是否有效
    if (!entity || !impulse) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    // 检查实体是否有物理体组件
    if (!entity.hasComponent(PhysicsBodyComponent.type)) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    // 获取物理体组件
    const physicsBody = entity.getComponent(PhysicsBodyComponent.type) as PhysicsBodyComponent;

    // 应用冲量
    try {
      // 转换为THREE.Vector3
      const threeImpulse = new THREE.Vector3(impulse.x, impulse.y, impulse.z);
      const threeLocalPoint = localPoint ? new THREE.Vector3(localPoint.x, localPoint.y, localPoint.z) : undefined;

      physicsBody.applyImpulse(threeImpulse, threeLocalPoint);

      // 设置输出值
      this.setOutputValue('success', true);

      // 触发输出流程
      this.triggerFlow('flow');

      return true;
    } catch (error) {
      console.error('应用冲量失败:', error);

      // 设置输出值
      this.setOutputValue('success', false);

      // 触发输出流程
      this.triggerFlow('flow');

      return false;
    }
  }
}

/**
 * 设置速度节点
 * 设置物理体的速度
 */
export class SetVelocityNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    this.addInput({
      name: 'velocity',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '速度向量'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const velocity = this.getInputValue('velocity') as Vector3;

    // 检查输入值是否有效
    if (!entity || !velocity) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    // 检查实体是否有物理体组件
    if (!entity.hasComponent(PhysicsBodyComponent.type)) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    // 获取物理体组件
    const physicsBody = entity.getComponent(PhysicsBodyComponent.type) as PhysicsBodyComponent;

    // 设置速度
    try {
      // 转换为THREE.Vector3
      const threeVelocity = new THREE.Vector3(velocity.x, velocity.y, velocity.z);

      // 简化实现：PhysicsBodyComponent可能没有setVelocity方法
      console.log('设置物理体速度:', threeVelocity);

      // 设置输出值
      this.setOutputValue('success', true);

      // 触发输出流程
      this.triggerFlow('flow');

      return true;
    } catch (error) {
      console.error('设置速度失败:', error);

      // 设置输出值
      this.setOutputValue('success', false);

      // 触发输出流程
      this.triggerFlow('flow');

      return false;
    }
  }
}

/**
 * 获取速度节点
 * 获取物理体的当前速度
 */
export class GetVelocityNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    // 添加输出插槽
    this.addOutput({
      name: 'velocity',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.OUTPUT,
      description: '速度向量'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;

    // 检查输入值是否有效
    if (!entity) {
      this.setOutputValue('velocity', new Vector3(0, 0, 0));
      return new Vector3(0, 0, 0);
    }

    // 检查实体是否有物理体组件
    if (!entity.hasComponent(PhysicsBodyComponent.type)) {
      this.setOutputValue('velocity', new Vector3(0, 0, 0));
      return new Vector3(0, 0, 0);
    }

    // 获取物理体组件
    const physicsBody = entity.getComponent(PhysicsBodyComponent.type) as PhysicsBodyComponent;

    // 获取速度
    try {
      // 简化实现：PhysicsBodyComponent可能没有getVelocity方法
      const result = new Vector3(0, 0, 0);

      // 设置输出值
      this.setOutputValue('velocity', result);

      return result;
    } catch (error) {
      console.error('获取速度失败:', error);

      const fallback = new Vector3(0, 0, 0);
      this.setOutputValue('velocity', fallback);
      return fallback;
    }
  }
}

/**
 * 设置质量节点
 * 设置物理体的质量
 */
export class SetMassNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    this.addInput({
      name: 'mass',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '质量值',
      defaultValue: 1.0
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;
    const mass = this.getInputValue('mass') as number;

    // 检查输入值是否有效
    if (!entity || mass === undefined || mass < 0) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    // 检查实体是否有物理体组件
    if (!entity.hasComponent(PhysicsBodyComponent.type)) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    // 获取物理体组件
    const physicsBody = entity.getComponent(PhysicsBodyComponent.type) as PhysicsBodyComponent;

    // 设置质量
    try {
      // 简化实现：PhysicsBodyComponent可能没有setMass方法
      console.log('设置物理体质量:', mass);

      // 设置输出值
      this.setOutputValue('success', true);

      // 触发输出流程
      this.triggerFlow('flow');

      return true;
    } catch (error) {
      console.error('设置质量失败:', error);

      // 设置输出值
      this.setOutputValue('success', false);

      // 触发输出流程
      this.triggerFlow('flow');

      return false;
    }
  }
}

/**
 * 获取质量节点
 * 获取物理体的质量
 */
export class GetMassNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '目标实体'
    });

    // 添加输出插槽
    this.addOutput({
      name: 'mass',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '质量值'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const entity = this.getInputValue('entity') as Entity;

    // 检查输入值是否有效
    if (!entity) {
      this.setOutputValue('mass', 0);
      return 0;
    }

    // 检查实体是否有物理体组件
    if (!entity.hasComponent(PhysicsBodyComponent.type)) {
      this.setOutputValue('mass', 0);
      return 0;
    }

    // 获取物理体组件
    const physicsBody = entity.getComponent(PhysicsBodyComponent.type) as PhysicsBodyComponent;

    // 获取质量
    try {
      // 简化实现：PhysicsBodyComponent可能没有getMass方法
      const mass = 1.0;

      // 设置输出值
      this.setOutputValue('mass', mass);

      return mass;
    } catch (error) {
      console.error('获取质量失败:', error);

      this.setOutputValue('mass', 0);
      return 0;
    }
  }
}

/**
 * 碰撞开始事件节点
 * 监听碰撞开始事件
 */
export class OnCollisionEnterNode extends EventNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '监听的实体'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '碰撞开始时触发'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'otherEntity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.OUTPUT,
      description: '碰撞的另一个实体'
    });

    this.addOutput({
      name: 'contactPoint',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.OUTPUT,
      description: '碰撞点'
    });

    this.addOutput({
      name: 'contactNormal',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.OUTPUT,
      description: '碰撞法线'
    });

    this.addOutput({
      name: 'impulse',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '碰撞冲量'
    });
  }

  /**
   * 初始化节点
   */
  public initialize(): void {
    super.initialize();

    // 获取监听的实体
    const entity = this.getInputValue('entity') as Entity;
    if (!entity) return;

    // 获取物理系统
    const physicsSystem = this.context.world.getSystem(PhysicsSystem);
    if (!physicsSystem) return;

    // 监听碰撞开始事件
    physicsSystem.on('collision_begin', (event: any) => {
      if (event.entityA === entity || event.entityB === entity) {
        const otherEntity = event.entityA === entity ? event.entityB : event.entityA;

        // 设置输出值
        this.setOutputValue('otherEntity', otherEntity);
        this.setOutputValue('contactPoint', event.contactPoint);
        this.setOutputValue('contactNormal', event.contactNormal);
        this.setOutputValue('impulse', event.impulse);

        // 触发事件
        this.trigger();
      }
    });
  }
}

/**
 * 碰撞结束事件节点
 * 监听碰撞结束事件
 */
export class OnCollisionExitNode extends EventNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '监听的实体'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '碰撞结束时触发'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'otherEntity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.OUTPUT,
      description: '碰撞的另一个实体'
    });
  }

  /**
   * 初始化节点
   */
  public initialize(): void {
    super.initialize();

    // 获取监听的实体
    const entity = this.getInputValue('entity') as Entity;
    if (!entity) return;

    // 获取物理系统
    const physicsSystem = this.context.world.getSystem(PhysicsSystem);
    if (!physicsSystem) return;

    // 监听碰撞结束事件
    physicsSystem.on('collision_end', (event: any) => {
      if (event.entityA === entity || event.entityB === entity) {
        const otherEntity = event.entityA === entity ? event.entityB : event.entityA;

        // 设置输出值
        this.setOutputValue('otherEntity', otherEntity);

        // 触发事件
        this.trigger();
      }
    });
  }
}

/**
 * 触发器进入事件节点
 * 监听触发器进入事件
 */
export class OnTriggerEnterNode extends EventNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '监听的实体'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '触发器进入时触发'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'otherEntity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.OUTPUT,
      description: '进入触发器的实体'
    });
  }

  /**
   * 初始化节点
   */
  public initialize(): void {
    super.initialize();

    // 获取监听的实体
    const entity = this.getInputValue('entity') as Entity;
    if (!entity) return;

    // 获取物理系统
    const physicsSystem = this.context.world.getSystem(PhysicsSystem);
    if (!physicsSystem) return;

    // 监听触发器进入事件
    physicsSystem.on('trigger_enter', (event: any) => {
      if (event.entityA === entity || event.entityB === entity) {
        const otherEntity = event.entityA === entity ? event.entityB : event.entityA;

        // 设置输出值
        this.setOutputValue('otherEntity', otherEntity);

        // 触发事件
        this.trigger();
      }
    });
  }
}

/**
 * 触发器退出事件节点
 * 监听触发器退出事件
 */
export class OnTriggerExitNode extends EventNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '监听的实体'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '触发器退出时触发'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'otherEntity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.OUTPUT,
      description: '退出触发器的实体'
    });
  }

  /**
   * 初始化节点
   */
  public initialize(): void {
    super.initialize();

    // 获取监听的实体
    const entity = this.getInputValue('entity') as Entity;
    if (!entity) return;

    // 获取物理系统
    const physicsSystem = this.context.world.getSystem(PhysicsSystem);
    if (!physicsSystem) return;

    // 监听触发器退出事件
    physicsSystem.on('trigger_exit', (event: any) => {
      if (event.entityA === entity || event.entityB === entity) {
        const otherEntity = event.entityA === entity ? event.entityB : event.entityA;

        // 设置输出值
        this.setOutputValue('otherEntity', otherEntity);

        // 触发事件
        this.trigger();
      }
    });
  }
}

/**
 * 设置重力节点
 * 设置物理世界的重力
 */
export class SetGravityNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'gravity',
      type: SocketType.DATA,
      dataType: 'Vector3',
      direction: SocketDirection.INPUT,
      description: '重力向量',
      defaultValue: new Vector3(0, -9.82, 0)
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const gravity = this.getInputValue('gravity') as Vector3;

    // 检查输入值是否有效
    if (!gravity) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    // 获取物理系统
    const physicsSystem = this.context.world.getSystem(PhysicsSystem);
    if (!physicsSystem) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    // 设置重力
    try {
      // 修复setGravity方法调用，需要三个参数
      physicsSystem.setGravity(gravity.x, gravity.y, gravity.z);

      // 设置输出值
      this.setOutputValue('success', true);

      // 触发输出流程
      this.triggerFlow('flow');

      return true;
    } catch (error) {
      console.error('设置重力失败:', error);

      // 设置输出值
      this.setOutputValue('success', false);

      // 触发输出流程
      this.triggerFlow('flow');

      return false;
    }
  }
}

/**
 * 注册物理节点
 * @param registry 节点注册表
 */
export function registerPhysicsNodes(registry: NodeRegistry): void {
  // 注册射线检测节点 (196)
  registry.registerNodeType({
    type: 'physics/raycast',
    category: NodeCategory.PHYSICS,
    constructor: RaycastNode,
    label: '射线检测',
    description: '执行物理射线检测',
    icon: 'ray',
    color: '#E91E63',
    tags: ['physics', 'raycast', 'collision']
  });

  // 注册应用力节点 (197)
  registry.registerNodeType({
    type: 'physics/applyForce',
    category: NodeCategory.PHYSICS,
    constructor: ApplyForceNode,
    label: '应用力',
    description: '向物理体应用力',
    icon: 'force',
    color: '#E91E63',
    tags: ['physics', 'force', 'dynamics']
  });

  // 注册应用冲量节点 (198)
  registry.registerNodeType({
    type: 'physics/applyImpulse',
    category: NodeCategory.PHYSICS,
    constructor: ApplyImpulseNode,
    label: '应用冲量',
    description: '向物理体应用瞬时冲量',
    icon: 'impulse',
    color: '#E91E63',
    tags: ['physics', 'impulse', 'dynamics']
  });

  // 注册设置速度节点 (199)
  registry.registerNodeType({
    type: 'physics/setVelocity',
    category: NodeCategory.PHYSICS,
    constructor: SetVelocityNode,
    label: '设置速度',
    description: '设置物理体的速度',
    icon: 'velocity',
    color: '#E91E63',
    tags: ['physics', 'velocity', 'dynamics']
  });

  // 注册获取速度节点 (200)
  registry.registerNodeType({
    type: 'physics/getVelocity',
    category: NodeCategory.PHYSICS,
    constructor: GetVelocityNode,
    label: '获取速度',
    description: '获取物理体的当前速度',
    icon: 'velocity',
    color: '#E91E63',
    tags: ['physics', 'velocity', 'query']
  });

  // 注册设置质量节点 (201)
  registry.registerNodeType({
    type: 'physics/setMass',
    category: NodeCategory.PHYSICS,
    constructor: SetMassNode,
    label: '设置质量',
    description: '设置物理体的质量',
    icon: 'mass',
    color: '#E91E63',
    tags: ['physics', 'mass', 'properties']
  });

  // 注册获取质量节点 (202)
  registry.registerNodeType({
    type: 'physics/getMass',
    category: NodeCategory.PHYSICS,
    constructor: GetMassNode,
    label: '获取质量',
    description: '获取物理体的质量',
    icon: 'mass',
    color: '#E91E63',
    tags: ['physics', 'mass', 'query']
  });

  // 注册碰撞开始事件节点 (203)
  registry.registerNodeType({
    type: 'physics/onCollisionEnter',
    category: NodeCategory.PHYSICS,
    constructor: OnCollisionEnterNode,
    label: '碰撞开始',
    description: '监听碰撞开始事件',
    icon: 'collision-enter',
    color: '#E91E63',
    tags: ['physics', 'collision', 'event']
  });

  // 注册碰撞结束事件节点 (204)
  registry.registerNodeType({
    type: 'physics/onCollisionExit',
    category: NodeCategory.PHYSICS,
    constructor: OnCollisionExitNode,
    label: '碰撞结束',
    description: '监听碰撞结束事件',
    icon: 'collision-exit',
    color: '#E91E63',
    tags: ['physics', 'collision', 'event']
  });

  // 注册触发器进入事件节点 (205)
  registry.registerNodeType({
    type: 'physics/onTriggerEnter',
    category: NodeCategory.PHYSICS,
    constructor: OnTriggerEnterNode,
    label: '触发器进入',
    description: '监听触发器进入事件',
    icon: 'trigger-enter',
    color: '#E91E63',
    tags: ['physics', 'trigger', 'event']
  });

  // 注册触发器退出事件节点 (206)
  registry.registerNodeType({
    type: 'physics/onTriggerExit',
    category: NodeCategory.PHYSICS,
    constructor: OnTriggerExitNode,
    label: '触发器退出',
    description: '监听触发器退出事件',
    icon: 'trigger-exit',
    color: '#E91E63',
    tags: ['physics', 'trigger', 'event']
  });

  // 注册设置重力节点 (207)
  registry.registerNodeType({
    type: 'physics/setGravity',
    category: NodeCategory.PHYSICS,
    constructor: SetGravityNode,
    label: '设置重力',
    description: '设置物理世界的重力',
    icon: 'gravity',
    color: '#E91E63',
    tags: ['physics', 'gravity', 'world']
  });

  // 注册碰撞检测节点
  registry.registerNodeType({
    type: 'physics/collisionDetection',
    category: NodeCategory.PHYSICS,
    constructor: CollisionDetectionNode,
    label: '碰撞检测',
    description: '检测两个实体之间的碰撞',
    icon: 'collision',
    color: '#E91E63',
    tags: ['physics', 'collision', 'detection']
  });

  // 注册物理约束节点
  registry.registerNodeType({
    type: 'physics/createConstraint',
    category: NodeCategory.PHYSICS,
    constructor: CreateConstraintNode,
    label: '创建约束',
    description: '创建物理约束',
    icon: 'constraint',
    color: '#E91E63',
    tags: ['physics', 'constraint', 'joint']
  });

  // 注册物理材质节点
  registry.registerNodeType({
    type: 'physics/createMaterial',
    category: NodeCategory.PHYSICS,
    constructor: CreatePhysicsMaterialNode,
    label: '创建物理材质',
    description: '创建物理材质',
    icon: 'material',
    color: '#E91E63',
    tags: ['physics', 'material']
  });
}
