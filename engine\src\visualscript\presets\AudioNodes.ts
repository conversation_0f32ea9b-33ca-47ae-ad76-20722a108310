/**
 * 视觉脚本音频节点
 * 提供音频播放和控制相关的节点
 */
import { FlowNode } from '../nodes/FlowNode';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import type { NodeOptions } from '../nodes/Node';

/**
 * 播放音频节点 (208)
 * 播放指定的音频文件
 */
export class PlayAudioNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'audioUrl',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '音频文件URL',
      defaultValue: ''
    });

    this.addInput({
      name: 'volume',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '音量（0.0-1.0）',
      defaultValue: 1.0
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功播放'
    });

    this.addOutput({
      name: 'audioId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '音频实例ID'
    });
  }

  public execute(): any {
    const audioUrl = this.getInputValue('audioUrl') as string;
    const volume = this.getInputValue('volume') as number;

    if (!audioUrl) {
      this.setOutputValue('success', false);
      this.setOutputValue('audioId', '');
      this.triggerFlow('flow');
      return false;
    }

    try {
      // 简化实现：使用HTML5 Audio API
      const audio = new Audio(audioUrl);
      audio.volume = Math.max(0, Math.min(1, volume));

      const audioId = `audio_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      audio.play().then(() => {
        this.setOutputValue('success', true);
        this.setOutputValue('audioId', audioId);
        this.triggerFlow('flow');
      }).catch((error) => {
        console.error('播放音频失败:', error);
        this.setOutputValue('success', false);
        this.setOutputValue('audioId', '');
        this.triggerFlow('flow');
      });

      return true;
    } catch (error) {
      console.error('播放音频失败:', error);
      this.setOutputValue('success', false);
      this.setOutputValue('audioId', '');
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 停止音频节点 (209)
 * 停止播放指定的音频
 */
export class StopAudioNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'audioId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '要停止的音频ID'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功停止'
    });
  }

  public execute(): any {
    const audioId = this.getInputValue('audioId') as string;

    if (!audioId) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      // 简化实现：记录停止操作
      console.log('停止音频:', audioId);
      this.setOutputValue('success', true);
      this.triggerFlow('flow');
      return true;
    } catch (error) {
      console.error('停止音频失败:', error);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 注册音频节点
 * @param registry 节点注册表
 */
export function registerAudioNodes(registry: NodeRegistry): void {
  // 注册播放音频节点 (208)
  registry.registerNodeType({
    type: 'audio/play',
    category: NodeCategory.AUDIO,
    constructor: PlayAudioNode,
    label: '播放音频',
    description: '播放指定的音频文件',
    icon: 'play',
    color: '#FF9800',
    tags: ['audio', 'play', 'sound']
  });

  // 注册停止音频节点 (209)
  registry.registerNodeType({
    type: 'audio/stop',
    category: NodeCategory.AUDIO,
    constructor: StopAudioNode,
    label: '停止音频',
    description: '停止播放指定的音频',
    icon: 'stop',
    color: '#FF9800',
    tags: ['audio', 'stop', 'sound']
  });
}
