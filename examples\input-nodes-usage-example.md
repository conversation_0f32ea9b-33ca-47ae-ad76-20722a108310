# 输入处理节点使用示例

本文档展示了如何使用新实现的输入处理节点（152-165）来创建各种交互功能。

## 节点概览

### 键盘输入节点
- **按键事件 (onKeyPress)**: 监听按键按下事件

### 鼠标输入节点
- **鼠标位置 (getPosition)**: 获取鼠标当前位置
- **鼠标按下 (isButtonDown)**: 检查鼠标按钮是否被按下
- **鼠标点击 (onClick)**: 监听鼠标点击事件
- **鼠标移动 (onMove)**: 监听鼠标移动事件
- **鼠标滚轮 (onWheel)**: 监听鼠标滚轮事件

### 触摸输入节点
- **触摸点数量 (getTouchCount)**: 获取当前触摸点数量
- **触摸位置 (getTouchPosition)**: 获取指定触摸点位置
- **触摸开始 (onTouchStart)**: 监听触摸开始事件
- **触摸结束 (onTouchEnd)**: 监听触摸结束事件
- **触摸移动 (onTouchMove)**: 监听触摸移动事件

### 手柄输入节点
- **手柄连接 (isConnected)**: 检查手柄是否连接
- **手柄按钮 (getButtonState)**: 获取手柄按钮状态
- **手柄摇杆 (getAxisValue)**: 获取手柄摇杆轴值

## 使用示例

### 示例1：简单的按键响应

**功能**: 当用户按下空格键时，在控制台输出消息。

**节点连接**:
```
[按键事件] → [调试日志]
  ↓
输入: key = "Space"
输出: onPressed → 执行
```

**实现步骤**:
1. 拖拽"按键事件"节点到画布
2. 设置key输入为"Space"
3. 拖拽"调试日志"节点到画布
4. 连接"按键事件"的onPressed输出到"调试日志"的执行输入
5. 设置日志消息为"空格键被按下！"

### 示例2：鼠标跟随效果

**功能**: 让一个物体跟随鼠标位置移动。

**节点连接**:
```
[鼠标位置] → [设置实体位置]
  ↓              ↓
输出: position → 输入: position
```

**实现步骤**:
1. 拖拽"鼠标位置"节点到画布
2. 拖拽"设置实体位置"节点到画布
3. 连接"鼠标位置"的position输出到"设置实体位置"的position输入
4. 设置目标实体为要跟随的物体

### 示例3：点击检测

**功能**: 检测鼠标左键点击，并在点击位置创建特效。

**节点连接**:
```
[鼠标点击] → [创建特效]
  ↓             ↓
输入: button=0  输入: position
输出: onClick → 执行
输出: position → position
```

**实现步骤**:
1. 拖拽"鼠标点击"节点到画布
2. 设置button输入为0（左键）
3. 拖拽"创建特效"节点到画布
4. 连接"鼠标点击"的onClick输出到"创建特效"的执行输入
5. 连接"鼠标点击"的position输出到"创建特效"的position输入

### 示例4：触摸手势识别

**功能**: 识别单指触摸和多指触摸。

**节点连接**:
```
[触摸开始] → [触摸点数量] → [条件判断] → [执行不同动作]
```

**实现步骤**:
1. 拖拽"触摸开始"节点到画布
2. 拖拽"触摸点数量"节点到画布
3. 连接触摸开始事件到获取触摸点数量
4. 使用条件判断节点根据触摸点数量执行不同操作

### 示例5：手柄控制角色移动

**功能**: 使用手柄摇杆控制角色移动。

**节点连接**:
```
[手柄连接检查] → [手柄摇杆X轴] → [角色移动X]
                [手柄摇杆Y轴] → [角色移动Y]
```

**实现步骤**:
1. 拖拽"手柄连接"节点检查手柄状态
2. 拖拽两个"手柄摇杆"节点分别获取X轴和Y轴值
3. 将轴值连接到角色移动控制节点

### 示例6：复合输入处理

**功能**: 同时处理键盘、鼠标和触摸输入的复合交互。

**节点连接**:
```
[按键事件: W] → [向前移动]
[按键事件: S] → [向后移动]
[鼠标移动] → [旋转视角]
[触摸移动] → [平移视角]
```

## 高级用法

### 输入状态管理

使用变量节点存储输入状态，实现复杂的输入组合：

```
[按键事件: Ctrl] → [设置变量: ctrlPressed = true]
[鼠标点击] → [条件判断: if ctrlPressed] → [特殊点击处理]
```

### 输入过滤和防抖

使用时间节点实现输入防抖：

```
[鼠标点击] → [延时节点: 200ms] → [重置点击状态]
```

### 多设备输入融合

同时支持多种输入设备：

```
[键盘输入] ↘
[鼠标输入] → [输入融合逻辑] → [统一处理]
[触摸输入] ↗
[手柄输入] ↗
```

## 注意事项

1. **性能优化**: 避免在每帧都检查输入状态，使用事件驱动的方式
2. **输入冲突**: 合理处理多种输入设备的冲突情况
3. **平台兼容**: 考虑不同平台的输入特性差异
4. **用户体验**: 提供清晰的输入反馈和提示

## 调试技巧

1. 使用"调试日志"节点输出输入值进行调试
2. 使用"条件判断"节点验证输入范围
3. 使用"变量显示"节点实时查看输入状态

这些输入处理节点为可视化脚本系统提供了完整的用户交互能力，开发者可以通过简单的拖拽和连接操作实现复杂的交互逻辑。
