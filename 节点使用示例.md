# 相机和UI节点使用示例

## 概述

本文档展示如何在编辑器中使用新实现的相机控制和UI交互节点来创建应用。

## 相机控制节点使用示例

### 1. 获取相机位置 (226)

**节点类型**: `camera/getPosition`
**功能**: 获取相机的当前位置

**使用方法**:
1. 从节点面板的"相机"分类中拖拽"获取相机位置"节点到画布
2. 连接相机对象到"相机"输入端口
3. 从"位置"输出端口获取位置数据

**输出格式**: `{ x: number, y: number, z: number }`

### 2. 相机朝向 (227)

**节点类型**: `camera/lookAt`
**功能**: 设置相机朝向目标位置

**使用方法**:
1. 拖拽"相机朝向"节点到画布
2. 连接相机对象到"相机"输入端口
3. 连接目标位置到"目标"输入端口
4. 连接流程控制线来触发执行

### 3. 设置视野角度 (228)

**节点类型**: `camera/setFOV`
**功能**: 设置相机的视野角度

**使用方法**:
1. 拖拽"设置视野角度"节点到画布
2. 连接相机对象到"相机"输入端口
3. 设置或连接视野角度值（默认75度）
4. 连接流程控制线来触发执行

## UI控件节点使用示例

### 1. 创建按钮 (229)

**节点类型**: `ui/button/create`
**功能**: 创建一个UI按钮

**使用方法**:
1. 从节点面板的"界面"分类中拖拽"创建按钮"节点到画布
2. 设置按钮文本（默认为"按钮"）
3. 连接流程控制线来触发创建
4. 从"按钮"输出端口获取创建的按钮对象

**示例流程**:
```
开始事件 → 创建按钮 → 设置按钮文本 → 按钮点击事件
```

### 2. 按钮点击事件 (230)

**节点类型**: `ui/button/onClick`
**功能**: 监听按钮点击事件

**使用方法**:
1. 拖拽"按钮点击"节点到画布
2. 连接按钮对象到"element"输入端口
3. 连接输出流程到需要执行的操作

### 3. 设置按钮文本 (231)

**节点类型**: `ui/button/setText`
**功能**: 设置按钮显示的文本

**使用方法**:
1. 拖拽"设置按钮文本"节点到画布
2. 连接按钮对象到"按钮"输入端口
3. 设置或连接新的文本内容
4. 连接流程控制线来触发执行

### 4. 设置按钮状态 (232)

**节点类型**: `ui/button/setEnabled`
**功能**: 设置按钮是否可用

**使用方法**:
1. 拖拽"设置按钮状态"节点到画布
2. 连接按钮对象到"按钮"输入端口
3. 设置启用状态（true/false）
4. 连接流程控制线来触发执行

### 5. 创建文本 (233)

**节点类型**: `ui/text/create`
**功能**: 创建一个UI文本标签

**使用方法**:
1. 拖拽"创建文本"节点到画布
2. 设置文本内容（默认为"文本"）
3. 连接流程控制线来触发创建
4. 从"文本元素"输出端口获取创建的文本对象

## 完整应用示例

### 简单的交互界面

**目标**: 创建一个带有按钮和文本的简单界面，点击按钮时更新文本内容

**节点连接流程**:
```
1. 开始事件
   ↓
2. 创建按钮 (文本: "点击我")
   ↓
3. 创建文本 (文本: "欢迎使用")
   ↓
4. 按钮点击事件 (连接到步骤2的按钮)
   ↓
5. 设置文本内容 (连接到步骤3的文本，新内容: "按钮被点击了！")
```

**实现步骤**:
1. 拖拽"开始事件"节点作为入口点
2. 连接"创建按钮"节点，设置文本为"点击我"
3. 连接"创建文本"节点，设置文本为"欢迎使用"
4. 添加"按钮点击事件"节点，连接按钮对象
5. 添加"设置文本内容"节点，连接文本对象和新内容
6. 连接所有流程控制线

### 相机控制示例

**目标**: 创建一个界面来控制相机的视野角度

**节点连接流程**:
```
1. 开始事件
   ↓
2. 创建按钮 (文本: "放大视野")
   ↓
3. 按钮点击事件
   ↓
4. 设置视野角度 (FOV: 90)
```

## 节点搜索和分类

在编辑器中，您可以通过以下方式快速找到节点：

### 按分类浏览
- **相机**: 包含所有相机控制相关节点
- **界面**: 包含所有UI控件相关节点

### 搜索功能
- 搜索"相机"：显示所有相机相关节点
- 搜索"按钮"：显示所有按钮相关节点
- 搜索"文本"：显示所有文本相关节点

## 注意事项

1. **流程控制**: 确保正确连接流程控制线，特别是对于需要执行顺序的操作
2. **对象引用**: UI对象需要先创建后才能进行操作，注意节点的执行顺序
3. **事件处理**: 事件节点需要调用start()方法来开始监听
4. **错误处理**: 大部分节点都有成功/失败的输出，可以用于错误处理

## 扩展可能性

基于当前的架构，可以轻松扩展更多功能：

1. **更多UI控件**: 输入框、滑块、下拉菜单等
2. **高级相机控制**: 动画、跟随、震动效果等
3. **布局管理**: 自动布局、响应式设计等
4. **样式控制**: CSS样式、主题切换等

通过这些节点，开发者可以快速构建交互式应用，无需编写代码即可实现复杂的用户界面和相机控制逻辑。
