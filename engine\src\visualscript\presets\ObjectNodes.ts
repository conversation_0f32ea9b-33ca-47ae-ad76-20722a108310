/**
 * 视觉脚本对象节点
 * 提供对象操作相关的节点
 */
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeCategory, NodeType, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry, NodeTypeInfo } from '../nodes/NodeRegistry';

/**
 * 获取对象属性节点
 * 获取对象的指定属性值
 */
export class ObjectGetPropertyNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加对象输入
    this.addInput({
      name: 'object',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '目标对象',
      defaultValue: {}
    });

    // 添加属性名输入
    this.addInput({
      name: 'property',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '属性名',
      defaultValue: ''
    });

    // 添加默认值输入
    this.addInput({
      name: 'defaultValue',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'any',
      description: '默认值（属性不存在时返回）',
      defaultValue: null
    });

    // 添加结果输出
    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'any',
      description: '属性值'
    });

    // 添加存在性输出
    this.addOutput({
      name: 'exists',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '属性是否存在'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const obj = this.getInputValue('object') || {};
    const property = String(this.getInputValue('property') || '');
    const defaultValue = this.getInputValue('defaultValue');

    // 检查属性是否存在
    const exists = obj && typeof obj === 'object' && property in obj;
    const value = exists ? obj[property] : defaultValue;

    // 设置输出值
    this.setOutputValue('value', value);
    this.setOutputValue('exists', exists);

    return value;
  }
}

/**
 * 设置对象属性节点
 * 设置对象的指定属性值
 */
export class ObjectSetPropertyNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加对象输入
    this.addInput({
      name: 'object',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '目标对象',
      defaultValue: {}
    });

    // 添加属性名输入
    this.addInput({
      name: 'property',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '属性名',
      defaultValue: ''
    });

    // 添加属性值输入
    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'any',
      description: '属性值',
      defaultValue: null
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '修改后的对象'
    });

    // 添加成功状态输出
    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '设置是否成功'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const inputObj = this.getInputValue('object') || {};
    const property = String(this.getInputValue('property') || '');
    const value = this.getInputValue('value');

    // 创建对象副本并设置属性
    let result = {};
    let success = false;

    try {
      if (inputObj && typeof inputObj === 'object') {
        result = { ...inputObj };
        if (property) {
          result[property] = value;
          success = true;
        }
      }
    } catch (error) {
      console.error('设置对象属性失败:', error);
      success = false;
    }

    // 设置输出值
    this.setOutputValue('result', result);
    this.setOutputValue('success', success);

    return result;
  }
}

/**
 * 创建对象节点
 * 创建一个新的对象
 */
export class ObjectCreateNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加初始属性输入（可选）
    this.addInput({
      name: 'properties',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '初始属性对象',
      defaultValue: {},
      optional: true
    });

    // 添加对象输出
    this.addOutput({
      name: 'object',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '创建的对象'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const properties = this.getInputValue('properties') || {};

    // 创建新对象
    let result = {};
    try {
      if (properties && typeof properties === 'object') {
        result = { ...properties };
      }
    } catch (error) {
      console.error('创建对象失败:', error);
      result = {};
    }

    // 设置输出值
    this.setOutputValue('object', result);

    return result;
  }
}

/**
 * 检查对象属性节点
 * 检查对象是否有指定属性
 */
export class ObjectHasPropertyNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加对象输入
    this.addInput({
      name: 'object',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '目标对象',
      defaultValue: {}
    });

    // 添加属性名输入
    this.addInput({
      name: 'property',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '属性名',
      defaultValue: ''
    });

    // 添加是否存在输出
    this.addOutput({
      name: 'hasProperty',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否有该属性'
    });

    // 添加属性类型输出
    this.addOutput({
      name: 'propertyType',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '属性类型'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const obj = this.getInputValue('object') || {};
    const property = String(this.getInputValue('property') || '');

    // 检查属性是否存在
    const hasProperty = obj && typeof obj === 'object' && property in obj;
    const propertyType = hasProperty ? typeof obj[property] : 'undefined';

    // 设置输出值
    this.setOutputValue('hasProperty', hasProperty);
    this.setOutputValue('propertyType', propertyType);

    return hasProperty;
  }
}

/**
 * 注册对象节点
 * @param registry 节点注册表
 */
export function registerObjectNodes(registry: NodeRegistry): void {
  // 注册获取对象属性节点
  registry.registerNodeType({
    type: 'object/getProperty',
    category: NodeCategory.OBJECT,
    constructor: ObjectGetPropertyNode,
    label: '获取对象属性',
    description: '获取对象的指定属性值',
    icon: 'eye',
    color: '#722ED1',
    tags: ['object', 'property', 'get']
  });

  // 注册设置对象属性节点
  registry.registerNodeType({
    type: 'object/setProperty',
    category: NodeCategory.OBJECT,
    constructor: ObjectSetPropertyNode,
    label: '设置属性',
    description: '设置对象的指定属性值',
    icon: 'edit',
    color: '#722ED1',
    tags: ['object', 'property', 'set']
  });

  // 注册创建对象节点
  registry.registerNodeType({
    type: 'object/create',
    category: NodeCategory.OBJECT,
    constructor: ObjectCreateNode,
    label: '创建对象',
    description: '创建一个新的对象',
    icon: 'plus-square',
    color: '#722ED1',
    tags: ['object', 'create', 'new']
  });

  // 注册检查对象属性节点
  registry.registerNodeType({
    type: 'object/hasProperty',
    category: NodeCategory.OBJECT,
    constructor: ObjectHasPropertyNode,
    label: '属性检查',
    description: '检查对象是否有指定属性',
    icon: 'check-circle',
    color: '#722ED1',
    tags: ['object', 'property', 'has', 'check']
  });
}
