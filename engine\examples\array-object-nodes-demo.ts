/**
 * 数组和对象节点演示示例
 * 展示新实现的数组操作和对象操作节点的使用
 */

import { NodeRegistry } from '../src/visualscript/nodes/NodeRegistry';
import { Graph } from '../src/visualscript/graph/Graph';
import { ExecutionContext } from '../src/visualscript/execution/ExecutionContext';
import { VisualScriptEngine } from '../src/visualscript/VisualScriptEngine';
import { registerAllPresetNodes } from '../src/visualscript/presets';

// 创建演示函数
export function demonstrateArrayObjectNodes() {
  console.log('=== 数组和对象节点演示 ===\n');

  // 初始化系统
  const registry = new NodeRegistry();
  registerAllPresetNodes(registry);
  
  const engine = new VisualScriptEngine();
  const graph = new Graph();
  const context = new ExecutionContext(engine);

  // 演示1: 数组操作链
  console.log('1. 数组操作演示:');
  demonstrateArrayOperations(registry, graph, context);

  // 演示2: 对象操作
  console.log('\n2. 对象操作演示:');
  demonstrateObjectOperations(registry, graph, context);

  // 演示3: 复杂数据处理
  console.log('\n3. 复杂数据处理演示:');
  demonstrateComplexDataProcessing(registry, graph, context);
}

/**
 * 演示数组操作
 */
function demonstrateArrayOperations(registry: NodeRegistry, graph: Graph, context: ExecutionContext) {
  try {
    console.log('  原始数组: [3, 1, 4, 1, 5]');
    let currentArray = [3, 1, 4, 1, 5];

    // 1. 添加首元素
    console.log('  → 添加首元素 0');
    const unshiftNode = registry.createNode('array/unshift', {
      id: 'unshift-demo',
      type: 'array/unshift',
      metadata: {},
      graph,
      context
    });

    if (unshiftNode) {
      unshiftNode.getInputValue = (name: string) => {
        if (name === 'array') return currentArray;
        if (name === 'element') return 0;
        return [];
      };

      let unshiftResult: any[] = [];
      unshiftNode.setOutputValue = (name: string, value: any) => {
        if (name === 'result') unshiftResult = value;
      };

      unshiftNode.execute();
      currentArray = unshiftResult;
      console.log(`    结果: [${currentArray.join(', ')}]`);
    }

    // 2. 数组排序
    console.log('  → 数组排序（升序）');
    const sortNode = registry.createNode('array/sort', {
      id: 'sort-demo',
      type: 'array/sort',
      metadata: {},
      graph,
      context
    });

    if (sortNode) {
      sortNode.getInputValue = (name: string) => {
        if (name === 'array') return currentArray;
        if (name === 'ascending') return true;
        if (name === 'numeric') return true;
        return true;
      };

      let sortResult: any[] = [];
      sortNode.setOutputValue = (name: string, value: any) => {
        if (name === 'result') sortResult = value;
      };

      sortNode.execute();
      currentArray = sortResult;
      console.log(`    结果: [${currentArray.join(', ')}]`);
    }

    // 3. 数组切片
    console.log('  → 数组切片（索引1到4）');
    const sliceNode = registry.createNode('array/slice', {
      id: 'slice-demo',
      type: 'array/slice',
      metadata: {},
      graph,
      context
    });

    if (sliceNode) {
      sliceNode.getInputValue = (name: string) => {
        if (name === 'array') return currentArray;
        if (name === 'start') return 1;
        if (name === 'end') return 4;
        return 0;
      };

      let sliceResult: any[] = [];
      sliceNode.setOutputValue = (name: string, value: any) => {
        if (name === 'result') sliceResult = value;
      };

      sliceNode.execute();
      console.log(`    结果: [${sliceResult.join(', ')}]`);
    }

    // 4. 数组连接
    console.log('  → 数组连接为字符串');
    const joinNode = registry.createNode('array/join', {
      id: 'join-demo',
      type: 'array/join',
      metadata: {},
      graph,
      context
    });

    if (joinNode) {
      joinNode.getInputValue = (name: string) => {
        if (name === 'array') return currentArray;
        if (name === 'separator') return ' -> ';
        return '';
      };

      let joinResult: string = '';
      joinNode.setOutputValue = (name: string, value: any) => {
        if (name === 'result') joinResult = value;
      };

      joinNode.execute();
      console.log(`    结果: "${joinResult}"`);
    }

    // 5. 查找元素
    console.log('  → 查找元素 3 的索引');
    const indexOfNode = registry.createNode('array/indexOf', {
      id: 'indexOf-demo',
      type: 'array/indexOf',
      metadata: {},
      graph,
      context
    });

    if (indexOfNode) {
      indexOfNode.getInputValue = (name: string) => {
        if (name === 'array') return currentArray;
        if (name === 'element') return 3;
        if (name === 'startIndex') return 0;
        return 0;
      };

      let indexResult: number = -1;
      let foundResult: boolean = false;
      indexOfNode.setOutputValue = (name: string, value: any) => {
        if (name === 'index') indexResult = value;
        if (name === 'found') foundResult = value;
      };

      indexOfNode.execute();
      console.log(`    索引: ${indexResult}, 找到: ${foundResult}`);
    }

  } catch (error) {
    console.error('数组操作演示出错:', error);
  }
}

/**
 * 演示对象操作
 */
function demonstrateObjectOperations(registry: NodeRegistry, graph: Graph, context: ExecutionContext) {
  try {
    // 1. 创建对象
    console.log('  → 创建用户对象');
    const createNode = registry.createNode('object/create', {
      id: 'create-demo',
      type: 'object/create',
      metadata: {},
      graph,
      context
    });

    let currentObject: any = {};
    if (createNode) {
      createNode.getInputValue = (name: string) => {
        if (name === 'properties') return { name: 'Alice', age: 25 };
        return {};
      };

      createNode.setOutputValue = (name: string, value: any) => {
        if (name === 'object') currentObject = value;
      };

      createNode.execute();
      console.log(`    结果: ${JSON.stringify(currentObject)}`);
    }

    // 2. 设置属性
    console.log('  → 设置email属性');
    const setNode = registry.createNode('object/setProperty', {
      id: 'set-demo',
      type: 'object/setProperty',
      metadata: {},
      graph,
      context
    });

    if (setNode) {
      setNode.getInputValue = (name: string) => {
        if (name === 'object') return currentObject;
        if (name === 'property') return 'email';
        if (name === 'value') return '<EMAIL>';
        return {};
      };

      setNode.setOutputValue = (name: string, value: any) => {
        if (name === 'result') currentObject = value;
      };

      setNode.execute();
      console.log(`    结果: ${JSON.stringify(currentObject)}`);
    }

    // 3. 检查属性
    console.log('  → 检查是否有phone属性');
    const hasNode = registry.createNode('object/hasProperty', {
      id: 'has-demo',
      type: 'object/hasProperty',
      metadata: {},
      graph,
      context
    });

    if (hasNode) {
      hasNode.getInputValue = (name: string) => {
        if (name === 'object') return currentObject;
        if (name === 'property') return 'phone';
        return {};
      };

      let hasProperty: boolean = false;
      let propertyType: string = '';
      hasNode.setOutputValue = (name: string, value: any) => {
        if (name === 'hasProperty') hasProperty = value;
        if (name === 'propertyType') propertyType = value;
      };

      hasNode.execute();
      console.log(`    有phone属性: ${hasProperty}, 类型: ${propertyType}`);
    }

    // 4. 获取属性
    console.log('  → 获取name属性');
    const getNode = registry.createNode('object/getProperty', {
      id: 'get-demo',
      type: 'object/getProperty',
      metadata: {},
      graph,
      context
    });

    if (getNode) {
      getNode.getInputValue = (name: string) => {
        if (name === 'object') return currentObject;
        if (name === 'property') return 'name';
        if (name === 'defaultValue') return 'Unknown';
        return {};
      };

      let propertyValue: any = null;
      getNode.setOutputValue = (name: string, value: any) => {
        if (name === 'value') propertyValue = value;
      };

      getNode.execute();
      console.log(`    name属性值: "${propertyValue}"`);
    }

  } catch (error) {
    console.error('对象操作演示出错:', error);
  }
}

/**
 * 演示复杂数据处理
 */
function demonstrateComplexDataProcessing(registry: NodeRegistry, graph: Graph, context: ExecutionContext) {
  try {
    console.log('  → 处理用户列表数据');
    
    // 模拟用户数据数组
    const users = [
      { name: 'Alice', age: 25, active: true },
      { name: 'Bob', age: 30, active: false },
      { name: 'Charlie', age: 35, active: true }
    ];

    console.log(`    原始数据: ${users.length} 个用户`);

    // 1. 获取数组长度
    const lengthNode = registry.createNode('array/length', {
      id: 'length-demo',
      type: 'array/length',
      metadata: {},
      graph,
      context
    });

    if (lengthNode) {
      lengthNode.getInputValue = (name: string) => {
        if (name === 'array') return users;
        return [];
      };

      let arrayLength: number = 0;
      lengthNode.setOutputValue = (name: string, value: any) => {
        if (name === 'length') arrayLength = value;
      };

      lengthNode.execute();
      console.log(`    数组长度: ${arrayLength}`);
    }

    // 2. 获取第一个用户
    const getNode = registry.createNode('array/get', {
      id: 'get-demo',
      type: 'array/get',
      metadata: {},
      graph,
      context
    });

    if (getNode) {
      getNode.getInputValue = (name: string) => {
        if (name === 'array') return users;
        if (name === 'index') return 0;
        if (name === 'defaultValue') return null;
        return 0;
      };

      let firstUser: any = null;
      getNode.setOutputValue = (name: string, value: any) => {
        if (name === 'element') firstUser = value;
      };

      getNode.execute();
      console.log(`    第一个用户: ${JSON.stringify(firstUser)}`);

      // 3. 检查第一个用户的属性
      if (firstUser) {
        const hasPropertyNode = registry.createNode('object/hasProperty', {
          id: 'has-prop-demo',
          type: 'object/hasProperty',
          metadata: {},
          graph,
          context
        });

        if (hasPropertyNode) {
          hasPropertyNode.getInputValue = (name: string) => {
            if (name === 'object') return firstUser;
            if (name === 'property') return 'active';
            return {};
          };

          let hasActive: boolean = false;
          hasPropertyNode.setOutputValue = (name: string, value: any) => {
            if (name === 'hasProperty') hasActive = value;
          };

          hasPropertyNode.execute();
          console.log(`    第一个用户有active属性: ${hasActive}`);
        }
      }
    }

    // 4. 反转用户数组
    const reverseNode = registry.createNode('array/reverse', {
      id: 'reverse-demo',
      type: 'array/reverse',
      metadata: {},
      graph,
      context
    });

    if (reverseNode) {
      reverseNode.getInputValue = (name: string) => {
        if (name === 'array') return users;
        return [];
      };

      let reversedUsers: any[] = [];
      reverseNode.setOutputValue = (name: string, value: any) => {
        if (name === 'result') reversedUsers = value;
      };

      reverseNode.execute();
      console.log(`    反转后第一个用户: ${JSON.stringify(reversedUsers[0])}`);
    }

  } catch (error) {
    console.error('复杂数据处理演示出错:', error);
  }
}

// 如果直接运行此文件，执行演示
if (require.main === module) {
  demonstrateArrayObjectNodes();
}
