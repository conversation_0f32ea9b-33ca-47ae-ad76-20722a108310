/**
 * 场景管理节点
 * 提供场景相关的可视化脚本节点
 */

import { FlowNode, FlowNodeOptions } from '../nodes/FlowNode';
import { NodeOptions, SocketType, NodeCategory, SocketDirection } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { Entity } from '../../core/Entity';
import { Scene } from '../../scene/Scene';

/**
 * 加载场景节点
 * 加载指定的场景
 */
export class LoadSceneNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const flowOptions: FlowNodeOptions = {
      ...options,
      inputFlowName: 'flow',
      outputFlowNames: ['success', 'fail']
    };
    super(flowOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'sceneName',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '要加载的场景名称'
    });

    // 输出插槽
    this.addOutput({
      name: 'scene',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'Scene',
      description: '加载的场景对象'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const sceneName = this.getInputValue('sceneName') as string;

    // 检查场景名称是否有效
    if (!sceneName) {
      console.warn('LoadSceneNode: 场景名称无效');
      this.triggerFlow('fail');
      return null;
    }

    try {
      // 这里应该通过场景管理器加载场景
      // 暂时返回一个新场景作为示例
      const scene = new Scene(sceneName);

      // 设置输出值
      this.setOutputValue('scene', scene);

      // 触发成功流程
      this.triggerFlow('success');

      return scene;
    } catch (error) {
      console.error('LoadSceneNode: 加载场景失败', error);
      this.triggerFlow('fail');
      return null;
    }
  }
}

/**
 * 获取当前场景节点
 * 获取当前活动的场景
 */
export class GetCurrentSceneNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const flowOptions: FlowNodeOptions = {
      ...options,
      inputFlowName: 'flow',
      outputFlowNames: ['flow']
    };
    super(flowOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输出插槽
    this.addOutput({
      name: 'scene',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'Scene',
      description: '当前活动的场景'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    try {
      // 这里应该通过场景管理器获取当前场景
      // 暂时返回null作为示例
      const currentScene = null; // SceneManager.getCurrentScene();

      // 设置输出值
      this.setOutputValue('scene', currentScene);

      // 触发输出流程
      this.triggerFlow('flow');

      return currentScene;
    } catch (error) {
      console.error('GetCurrentSceneNode: 获取当前场景失败', error);
      this.triggerFlow('flow');
      return null;
    }
  }
}

/**
 * 查找实体节点
 * 在场景中查找指定名称的实体
 */
export class FindEntityNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const flowOptions: FlowNodeOptions = {
      ...options,
      inputFlowName: 'flow',
      outputFlowNames: ['found', 'notFound']
    };
    super(flowOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'scene',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'Scene',
      description: '要搜索的场景（可选，默认当前场景）'
    });

    this.addInput({
      name: 'entityName',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '要查找的实体名称'
    });

    // 输出插槽
    this.addOutput({
      name: 'entity',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'Entity',
      description: '找到的实体'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const scene = this.getInputValue('scene') as Scene;
    const entityName = this.getInputValue('entityName') as string;

    // 检查实体名称是否有效
    if (!entityName) {
      console.warn('FindEntityNode: 实体名称无效');
      this.triggerFlow('notFound');
      return null;
    }

    try {
      // 如果没有指定场景，使用当前场景
      const targetScene = scene; // || SceneManager.getCurrentScene();

      if (!targetScene) {
        console.warn('FindEntityNode: 没有可用的场景');
        this.triggerFlow('notFound');
        return null;
      }

      // 在场景中查找实体
      const entities = targetScene.findEntitiesByName(entityName);
      const entity = entities.length > 0 ? entities[0] : null;

      if (entity) {
        // 设置输出值
        this.setOutputValue('entity', entity);
        // 触发找到流程
        this.triggerFlow('found');
        return entity;
      } else {
        // 触发未找到流程
        this.triggerFlow('notFound');
        return null;
      }
    } catch (error) {
      console.error('FindEntityNode: 查找实体失败', error);
      this.triggerFlow('notFound');
      return null;
    }
  }
}

/**
 * 按标签查找实体节点
 * 在场景中查找指定标签的实体
 */
export class FindEntitiesByTagNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const flowOptions: FlowNodeOptions = {
      ...options,
      inputFlowName: 'flow',
      outputFlowNames: ['found', 'notFound']
    };
    super(flowOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'scene',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'Scene',
      description: '要搜索的场景（可选，默认当前场景）'
    });

    this.addInput({
      name: 'tag',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '要查找的实体标签'
    });

    // 输出插槽
    this.addOutput({
      name: 'entities',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'Array<Entity>',
      description: '找到的实体列表'
    });

    this.addOutput({
      name: 'count',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '找到的实体数量'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const scene = this.getInputValue('scene') as Scene;
    const tag = this.getInputValue('tag') as string;

    // 检查标签是否有效
    if (!tag) {
      console.warn('FindEntitiesByTagNode: 标签无效');
      this.setOutputValue('entities', []);
      this.setOutputValue('count', 0);
      this.triggerFlow('notFound');
      return [];
    }

    try {
      // 如果没有指定场景，使用当前场景
      const targetScene = scene; // || SceneManager.getCurrentScene();

      if (!targetScene) {
        console.warn('FindEntitiesByTagNode: 没有可用的场景');
        this.setOutputValue('entities', []);
        this.setOutputValue('count', 0);
        this.triggerFlow('notFound');
        return [];
      }

      // 在场景中按标签查找实体
      const entities = targetScene.findEntitiesByTag(tag);

      // 设置输出值
      this.setOutputValue('entities', entities);
      this.setOutputValue('count', entities.length);

      if (entities.length > 0) {
        // 触发找到流程
        this.triggerFlow('found');
      } else {
        // 触发未找到流程
        this.triggerFlow('notFound');
      }

      return entities;
    } catch (error) {
      console.error('FindEntitiesByTagNode: 按标签查找实体失败', error);
      this.setOutputValue('entities', []);
      this.setOutputValue('count', 0);
      this.triggerFlow('notFound');
      return [];
    }
  }
}

/**
 * 实例化节点
 * 实例化预制体到场景中
 */
export class InstantiateNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const flowOptions: FlowNodeOptions = {
      ...options,
      inputFlowName: 'flow',
      outputFlowNames: ['success', 'fail']
    };
    super(flowOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'prefab',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '要实例化的预制体名称或路径'
    });

    this.addInput({
      name: 'position',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'Vector3',
      description: '实例化的位置（可选）'
    });

    this.addInput({
      name: 'rotation',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'Euler',
      description: '实例化的旋转（可选）'
    });

    this.addInput({
      name: 'parent',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'Entity',
      description: '父实体（可选）'
    });

    // 输出插槽
    this.addOutput({
      name: 'instance',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'Entity',
      description: '实例化的实体'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const prefab = this.getInputValue('prefab') as string;
    // const position = this.getInputValue('position');
    // const rotation = this.getInputValue('rotation');
    // const parent = this.getInputValue('parent') as Entity;

    // 检查预制体是否有效
    if (!prefab) {
      console.warn('InstantiateNode: 预制体无效');
      this.triggerFlow('fail');
      return null;
    }

    try {
      // 这里应该通过预制体管理器实例化预制体
      // 暂时返回null作为示例，实际实现需要从上下文获取World实例
      console.log(`实例化预制体: ${prefab}`);

      // TODO: 实际实现应该是：
      // const world = this.context.getWorld();
      // const instance = world.createEntity(`${prefab}_instance`);

      const instance = null; // 暂时返回null

      // 设置输出值
      this.setOutputValue('instance', instance);

      // 触发成功流程
      this.triggerFlow('success');

      return instance;
    } catch (error) {
      console.error('InstantiateNode: 实例化失败', error);
      this.triggerFlow('fail');
      return null;
    }
  }
}

/**
 * 注册场景节点
 * @param registry 节点注册表
 */
export function registerSceneNodes(registry: NodeRegistry): void {
  // 注册加载场景节点
  registry.registerNodeType({
    type: 'scene/loadScene',
    category: NodeCategory.ENTITY,
    constructor: LoadSceneNode,
    label: '加载场景',
    description: '加载指定的场景',
    icon: 'scene',
    color: '#9C27B0',
    tags: ['scene', 'load']
  });

  // 注册获取当前场景节点
  registry.registerNodeType({
    type: 'scene/getCurrentScene',
    category: NodeCategory.ENTITY,
    constructor: GetCurrentSceneNode,
    label: '获取当前场景',
    description: '获取当前活动的场景',
    icon: 'scene',
    color: '#9C27B0',
    tags: ['scene', 'current']
  });

  // 注册查找实体节点
  registry.registerNodeType({
    type: 'scene/findEntity',
    category: NodeCategory.ENTITY,
    constructor: FindEntityNode,
    label: '查找实体',
    description: '在场景中查找指定名称的实体',
    icon: 'search',
    color: '#9C27B0',
    tags: ['scene', 'find', 'entity']
  });

  // 注册按标签查找实体节点
  registry.registerNodeType({
    type: 'scene/findEntitiesByTag',
    category: NodeCategory.ENTITY,
    constructor: FindEntitiesByTagNode,
    label: '按标签查找',
    description: '在场景中查找指定标签的实体',
    icon: 'tag',
    color: '#9C27B0',
    tags: ['scene', 'find', 'tag']
  });

  // 注册实例化节点
  registry.registerNodeType({
    type: 'scene/instantiate',
    category: NodeCategory.ENTITY,
    constructor: InstantiateNode,
    label: '实例化',
    description: '实例化预制体到场景中',
    icon: 'copy',
    color: '#9C27B0',
    tags: ['scene', 'instantiate', 'prefab']
  });
}
