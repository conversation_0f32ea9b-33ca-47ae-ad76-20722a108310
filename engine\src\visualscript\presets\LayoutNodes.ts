/**
 * 视觉脚本布局节点
 * 提供自动布局和响应式设计功能
 */
import { FlowNode } from '../nodes/FlowNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import type { NodeOptions } from '../nodes/Node';

/**
 * 创建Flexbox容器节点 (251)
 * 创建一个Flexbox布局容器
 */
export class CreateFlexContainerNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'direction',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '主轴方向',
      defaultValue: 'row'
    });

    this.addInput({
      name: 'justifyContent',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '主轴对齐方式',
      defaultValue: 'flex-start'
    });

    this.addInput({
      name: 'alignItems',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '交叉轴对齐方式',
      defaultValue: 'stretch'
    });

    this.addInput({
      name: 'gap',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '元素间距（像素）',
      defaultValue: 0
    });

    this.addOutput({
      name: 'container',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '创建的Flex容器'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功创建'
    });
  }

  public execute(): any {
    const direction = this.getInputValue('direction') as string;
    const justifyContent = this.getInputValue('justifyContent') as string;
    const alignItems = this.getInputValue('alignItems') as string;
    const gap = this.getInputValue('gap') as number;

    try {
      const container = document.createElement('div');
      container.style.display = 'flex';
      container.style.flexDirection = direction;
      container.style.justifyContent = justifyContent;
      container.style.alignItems = alignItems;
      container.style.gap = `${gap}px`;
      container.style.width = '100%';
      container.style.height = 'auto';

      // 添加到页面
      document.body.appendChild(container);

      this.setOutputValue('container', container);
      this.setOutputValue('success', true);
      this.triggerFlow('flow');
      return container;
    } catch (error) {
      console.error('创建Flex容器失败:', error);
      this.setOutputValue('container', null);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return null;
    }
  }
}

/**
 * 添加子元素到容器节点 (252)
 * 将元素添加到布局容器中
 */
export class AddToContainerNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'container',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '目标容器'
    });

    this.addInput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '要添加的元素'
    });

    this.addInput({
      name: 'flex',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: 'Flex增长因子',
      defaultValue: 0
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功添加'
    });
  }

  public execute(): any {
    const container = this.getInputValue('container') as HTMLElement;
    const element = this.getInputValue('element') as HTMLElement;
    const flex = this.getInputValue('flex') as number;

    if (!container || !element) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      // 设置flex属性
      if (flex > 0) {
        element.style.flex = String(flex);
      }

      // 添加到容器
      container.appendChild(element);

      this.setOutputValue('success', true);
      this.triggerFlow('flow');
      return true;
    } catch (error) {
      console.error('添加元素到容器失败:', error);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 创建Grid容器节点 (253)
 * 创建一个CSS Grid布局容器
 */
export class CreateGridContainerNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'columns',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '列数',
      defaultValue: 3
    });

    this.addInput({
      name: 'rows',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '行数',
      defaultValue: 3
    });

    this.addInput({
      name: 'gap',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '网格间距（像素）',
      defaultValue: 10
    });

    this.addOutput({
      name: 'container',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '创建的Grid容器'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功创建'
    });
  }

  public execute(): any {
    const columns = this.getInputValue('columns') as number;
    const rows = this.getInputValue('rows') as number;
    const gap = this.getInputValue('gap') as number;

    try {
      const container = document.createElement('div');
      container.style.display = 'grid';
      container.style.gridTemplateColumns = `repeat(${columns}, 1fr)`;
      container.style.gridTemplateRows = `repeat(${rows}, 1fr)`;
      container.style.gap = `${gap}px`;
      container.style.width = '100%';
      container.style.height = '400px'; // 默认高度

      // 添加到页面
      document.body.appendChild(container);

      this.setOutputValue('container', container);
      this.setOutputValue('success', true);
      this.triggerFlow('flow');
      return container;
    } catch (error) {
      console.error('创建Grid容器失败:', error);
      this.setOutputValue('container', null);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return null;
    }
  }
}

/**
 * 设置Grid项目位置节点 (254)
 * 设置Grid项目在网格中的位置
 */
export class SetGridItemPositionNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'Grid项目元素'
    });

    this.addInput({
      name: 'column',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '列位置',
      defaultValue: 1
    });

    this.addInput({
      name: 'row',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '行位置',
      defaultValue: 1
    });

    this.addInput({
      name: 'columnSpan',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '跨越列数',
      defaultValue: 1
    });

    this.addInput({
      name: 'rowSpan',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '跨越行数',
      defaultValue: 1
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功设置'
    });
  }

  public execute(): any {
    const element = this.getInputValue('element') as HTMLElement;
    const column = this.getInputValue('column') as number;
    const row = this.getInputValue('row') as number;
    const columnSpan = this.getInputValue('columnSpan') as number;
    const rowSpan = this.getInputValue('rowSpan') as number;

    if (!element) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      element.style.gridColumn = `${column} / span ${columnSpan}`;
      element.style.gridRow = `${row} / span ${rowSpan}`;

      this.setOutputValue('success', true);
      this.triggerFlow('flow');
      return true;
    } catch (error) {
      console.error('设置Grid项目位置失败:', error);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 响应式断点检测节点 (255)
 * 检测当前屏幕尺寸对应的断点
 */
export class ResponsiveBreakpointNode extends FunctionNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addOutput({
      name: 'breakpoint',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '当前断点（xs, sm, md, lg, xl）'
    });

    this.addOutput({
      name: 'width',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '当前屏幕宽度'
    });

    this.addOutput({
      name: 'height',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '当前屏幕高度'
    });
  }

  public execute(): any {
    try {
      const width = window.innerWidth;
      const height = window.innerHeight;

      let breakpoint = 'xs';
      if (width >= 1200) {
        breakpoint = 'xl';
      } else if (width >= 992) {
        breakpoint = 'lg';
      } else if (width >= 768) {
        breakpoint = 'md';
      } else if (width >= 576) {
        breakpoint = 'sm';
      }

      this.setOutputValue('breakpoint', breakpoint);
      this.setOutputValue('width', width);
      this.setOutputValue('height', height);

      return { breakpoint, width, height };
    } catch (error) {
      console.error('获取响应式断点失败:', error);
      this.setOutputValue('breakpoint', 'xs');
      this.setOutputValue('width', 0);
      this.setOutputValue('height', 0);
      return { breakpoint: 'xs', width: 0, height: 0 };
    }
  }
}

/**
 * 设置响应式样式节点 (256)
 * 根据断点设置不同的样式
 */
export class SetResponsiveStyleNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '要设置样式的元素'
    });

    this.addInput({
      name: 'property',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: 'CSS属性名',
      defaultValue: 'width'
    });

    this.addInput({
      name: 'xsValue',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '超小屏幕值',
      defaultValue: '100%'
    });

    this.addInput({
      name: 'smValue',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '小屏幕值',
      defaultValue: '100%'
    });

    this.addInput({
      name: 'mdValue',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '中等屏幕值',
      defaultValue: '50%'
    });

    this.addInput({
      name: 'lgValue',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '大屏幕值',
      defaultValue: '33.33%'
    });

    this.addInput({
      name: 'xlValue',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '超大屏幕值',
      defaultValue: '25%'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功设置'
    });
  }

  public execute(): any {
    const element = this.getInputValue('element') as HTMLElement;
    const property = this.getInputValue('property') as string;
    const xsValue = this.getInputValue('xsValue') as string;
    const smValue = this.getInputValue('smValue') as string;
    const mdValue = this.getInputValue('mdValue') as string;
    const lgValue = this.getInputValue('lgValue') as string;
    const xlValue = this.getInputValue('xlValue') as string;

    if (!element || !property) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      const width = window.innerWidth;
      let value = xsValue;

      if (width >= 1200) {
        value = xlValue;
      } else if (width >= 992) {
        value = lgValue;
      } else if (width >= 768) {
        value = mdValue;
      } else if (width >= 576) {
        value = smValue;
      }

      (element.style as any)[property] = value;

      this.setOutputValue('success', true);
      this.triggerFlow('flow');
      return true;
    } catch (error) {
      console.error('设置响应式样式失败:', error);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 注册布局节点
 * @param registry 节点注册表
 */
export function registerLayoutNodes(registry: NodeRegistry): void {
  // 注册创建Flex容器节点 (251)
  registry.registerNodeType({
    type: 'layout/flex/create',
    category: NodeCategory.UI,
    constructor: CreateFlexContainerNode,
    label: '创建Flex容器',
    description: '创建一个Flexbox布局容器',
    icon: 'flex',
    color: '#607D8B',
    tags: ['layout', 'flex', 'container', 'create']
  });

  // 注册添加子元素到容器节点 (252)
  registry.registerNodeType({
    type: 'layout/addToContainer',
    category: NodeCategory.UI,
    constructor: AddToContainerNode,
    label: '添加到容器',
    description: '将元素添加到布局容器中',
    icon: 'add',
    color: '#607D8B',
    tags: ['layout', 'container', 'add', 'child']
  });

  // 注册创建Grid容器节点 (253)
  registry.registerNodeType({
    type: 'layout/grid/create',
    category: NodeCategory.UI,
    constructor: CreateGridContainerNode,
    label: '创建Grid容器',
    description: '创建一个CSS Grid布局容器',
    icon: 'grid',
    color: '#607D8B',
    tags: ['layout', 'grid', 'container', 'create']
  });

  // 注册设置Grid项目位置节点 (254)
  registry.registerNodeType({
    type: 'layout/grid/setPosition',
    category: NodeCategory.UI,
    constructor: SetGridItemPositionNode,
    label: '设置Grid位置',
    description: '设置Grid项目在网格中的位置',
    icon: 'position',
    color: '#607D8B',
    tags: ['layout', 'grid', 'position', 'set']
  });

  // 注册响应式断点检测节点 (255)
  registry.registerNodeType({
    type: 'layout/responsive/breakpoint',
    category: NodeCategory.UI,
    constructor: ResponsiveBreakpointNode,
    label: '响应式断点',
    description: '检测当前屏幕尺寸对应的断点',
    icon: 'responsive',
    color: '#607D8B',
    tags: ['layout', 'responsive', 'breakpoint', 'screen']
  });

  // 注册设置响应式样式节点 (256)
  registry.registerNodeType({
    type: 'layout/responsive/setStyle',
    category: NodeCategory.UI,
    constructor: SetResponsiveStyleNode,
    label: '设置响应式样式',
    description: '根据断点设置不同的样式',
    icon: 'style',
    color: '#607D8B',
    tags: ['layout', 'responsive', 'style', 'set']
  });
}
