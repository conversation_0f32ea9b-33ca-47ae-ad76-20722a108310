/**
 * 节点构建测试
 * 验证新实现的节点是否能正确构建和注册
 */

import { NodeRegistry } from '../nodes/NodeRegistry';
import { NodeCategory } from '../nodes/Node';
import { registerTimeNodes } from '../presets/TimeNodes';
import { registerAnimationNodes } from '../presets/AnimationNodes';
import { registerInputNodes } from '../presets/InputNodes';

/**
 * 测试节点构建和注册
 */
export function testNodeBuild(): boolean {
  console.log('开始测试节点构建...');
  
  try {
    // 创建节点注册表
    const registry = new NodeRegistry();
    
    // 注册新节点
    console.log('注册时间节点...');
    registerTimeNodes(registry);
    
    console.log('注册动画节点...');
    registerAnimationNodes(registry);
    
    console.log('注册输入节点...');
    registerInputNodes(registry);
    
    // 验证时间节点
    const timeNodes = [
      'time/getCurrentTime',
      'time/getDeltaTime', 
      'time/delay',
      'time/timer',
      'time/stopwatch',
      'time/formatTime'
    ];
    
    console.log('验证时间节点注册...');
    for (const nodeType of timeNodes) {
      const nodeInfo = registry.getNodeTypeInfo(nodeType);
      if (!nodeInfo) {
        throw new Error(`时间节点 ${nodeType} 未正确注册`);
      }
      console.log(`✓ ${nodeType}: ${nodeInfo.label}`);
    }
    
    // 验证动画节点
    const animationNodes = [
      'animation/play',
      'animation/stop',
      'animation/pause',
      'animation/resume',
      'animation/setSpeed',
      'animation/getState',
      'animation/setTime',
      'animation/crossFade'
    ];
    
    console.log('验证动画节点注册...');
    for (const nodeType of animationNodes) {
      const nodeInfo = registry.getNodeTypeInfo(nodeType);
      if (!nodeInfo) {
        throw new Error(`动画节点 ${nodeType} 未正确注册`);
      }
      console.log(`✓ ${nodeType}: ${nodeInfo.label}`);
    }
    
    // 验证输入节点
    const inputNodes = [
      'input/keyboard/isKeyDown',
      'input/keyboard/isKeyUp'
    ];
    
    console.log('验证输入节点注册...');
    for (const nodeType of inputNodes) {
      const nodeInfo = registry.getNodeTypeInfo(nodeType);
      if (!nodeInfo) {
        throw new Error(`输入节点 ${nodeType} 未正确注册`);
      }
      console.log(`✓ ${nodeType}: ${nodeInfo.label}`);
    }
    
    // 验证节点分类
    console.log('验证节点分类...');
    const timeNodesInCategory = registry.getNodeTypesByCategory(NodeCategory.TIME);
    const animationNodesInCategory = registry.getNodeTypesByCategory(NodeCategory.ANIMATION);
    const inputNodesInCategory = registry.getNodeTypesByCategory(NodeCategory.INPUT);
    
    console.log(`时间分类节点数量: ${timeNodesInCategory.length}`);
    console.log(`动画分类节点数量: ${animationNodesInCategory.length}`);
    console.log(`输入分类节点数量: ${inputNodesInCategory.length}`);
    
    if (timeNodesInCategory.length < 6) {
      throw new Error('时间分类节点数量不足');
    }
    
    if (animationNodesInCategory.length < 8) {
      throw new Error('动画分类节点数量不足');
    }
    
    if (inputNodesInCategory.length < 2) {
      throw new Error('输入分类节点数量不足');
    }
    
    console.log('✅ 所有节点构建测试通过！');
    return true;
    
  } catch (error) {
    console.error('❌ 节点构建测试失败:', error);
    return false;
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  testNodeBuild();
}
