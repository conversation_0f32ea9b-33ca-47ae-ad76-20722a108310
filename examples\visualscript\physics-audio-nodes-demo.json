{"id": "physics-audio-nodes-demo", "name": "物理和音频节点演示", "description": "展示新增的物理模拟和音频控制节点的使用方法", "version": "1.0.0", "variables": [{"id": "player-entity", "name": "玩家实体", "type": "Entity", "value": null, "description": "玩家角色实体"}, {"id": "jump-force", "name": "跳跃力度", "type": "number", "value": 500, "description": "角色跳跃时的力度"}, {"id": "jump-sound-id", "name": "跳跃音效ID", "type": "string", "value": "", "description": "跳跃音效的播放ID"}], "customEvents": [{"id": "player-jump", "name": "玩家跳跃", "description": "当玩家按下跳跃键时触发"}, {"id": "player-land", "name": "玩家着陆", "description": "当玩家着陆时触发"}], "nodes": [{"id": "start-node", "type": "core/events/onStart", "position": {"x": 100, "y": 100}, "data": {"label": "开始"}, "flows": {"flow": {"nodeId": "setup-gravity", "socket": "flow"}}}, {"id": "setup-gravity", "type": "physics/setGravity", "position": {"x": 300, "y": 100}, "data": {"label": "设置重力"}, "parameters": {"gravity": {"value": {"x": 0, "y": -9.82, "z": 0}}}, "flows": {"flow": {"nodeId": "get-player", "socket": "flow"}}}, {"id": "get-player", "type": "core/variable/get", "position": {"x": 500, "y": 100}, "data": {"label": "获取玩家实体", "variableName": "player-entity"}, "flows": {"flow": {"nodeId": "setup-collision-listener", "socket": "flow"}}}, {"id": "setup-collision-listener", "type": "physics/onCollisionEnter", "position": {"x": 700, "y": 100}, "data": {"label": "监听碰撞"}, "inputs": {"entity": {"nodeId": "get-player", "socket": "value"}}, "flows": {"flow": {"nodeId": "play-land-sound", "socket": "flow"}}}, {"id": "play-land-sound", "type": "audio/playSound", "position": {"x": 900, "y": 100}, "data": {"label": "播放着陆音效"}, "parameters": {"audioUrl": {"value": "sounds/land.mp3"}, "volume": {"value": 0.7}, "loop": {"value": false}}}, {"id": "jump-trigger", "type": "input/onKeyPress", "position": {"x": 100, "y": 300}, "data": {"label": "跳跃按键"}, "parameters": {"key": {"value": "Space"}}, "flows": {"flow": {"nodeId": "check-grounded", "socket": "flow"}}}, {"id": "check-grounded", "type": "physics/raycast", "position": {"x": 300, "y": 300}, "data": {"label": "检查是否着地"}, "inputs": {"origin": {"nodeId": "get-player-position", "socket": "position"}}, "parameters": {"direction": {"value": {"x": 0, "y": -1, "z": 0}}, "maxDistance": {"value": 1.1}}, "flows": {"flow": {"nodeId": "branch-grounded", "socket": "flow"}}}, {"id": "get-player-position", "type": "transform/getPosition", "position": {"x": 100, "y": 400}, "data": {"label": "获取玩家位置"}, "inputs": {"entity": {"nodeId": "get-player-var", "socket": "value"}}}, {"id": "get-player-var", "type": "core/variable/get", "position": {"x": 100, "y": 500}, "data": {"label": "获取玩家变量", "variableName": "player-entity"}}, {"id": "branch-grounded", "type": "core/flow/branch", "position": {"x": 500, "y": 300}, "data": {"label": "是否着地分支"}, "inputs": {"condition": {"nodeId": "check-grounded", "socket": "hit"}}, "flows": {"true": {"nodeId": "apply-jump-force", "socket": "flow"}}}, {"id": "apply-jump-force", "type": "physics/applyImpulse", "position": {"x": 700, "y": 300}, "data": {"label": "应用跳跃冲量"}, "inputs": {"entity": {"nodeId": "get-player-var", "socket": "value"}, "impulse": {"nodeId": "get-jump-force", "socket": "value"}}, "flows": {"flow": {"nodeId": "play-jump-sound", "socket": "flow"}}}, {"id": "get-jump-force", "type": "core/variable/get", "position": {"x": 500, "y": 400}, "data": {"label": "获取跳跃力度", "variableName": "jump-force"}}, {"id": "play-jump-sound", "type": "audio/playSound", "position": {"x": 900, "y": 300}, "data": {"label": "播放跳跃音效"}, "parameters": {"audioUrl": {"value": "sounds/jump.mp3"}, "volume": {"value": 0.8}, "loop": {"value": false}}, "flows": {"flow": {"nodeId": "store-jump-sound-id", "socket": "flow"}}}, {"id": "store-jump-sound-id", "type": "core/variable/set", "position": {"x": 1100, "y": 300}, "data": {"label": "存储音效ID", "variableName": "jump-sound-id"}, "inputs": {"value": {"nodeId": "play-jump-sound", "socket": "audioId"}}}, {"id": "velocity-monitor", "type": "core/events/onUpdate", "position": {"x": 100, "y": 600}, "data": {"label": "速度监控"}, "flows": {"flow": {"nodeId": "get-velocity", "socket": "flow"}}}, {"id": "get-velocity", "type": "physics/getVelocity", "position": {"x": 300, "y": 600}, "data": {"label": "获取速度"}, "inputs": {"entity": {"nodeId": "get-player-var-2", "socket": "value"}}, "flows": {"flow": {"nodeId": "check-high-speed", "socket": "flow"}}}, {"id": "get-player-var-2", "type": "core/variable/get", "position": {"x": 100, "y": 700}, "data": {"label": "获取玩家变量2", "variableName": "player-entity"}}, {"id": "check-high-speed", "type": "math/comparison/greater", "position": {"x": 500, "y": 600}, "data": {"label": "检查高速度"}, "inputs": {"a": {"nodeId": "get-velocity-magnitude", "socket": "result"}}, "parameters": {"b": {"value": 15}}, "flows": {"flow": {"nodeId": "speed-limit-branch", "socket": "flow"}}}, {"id": "get-velocity-magnitude", "type": "math/vector/magnitude", "position": {"x": 300, "y": 700}, "data": {"label": "获取速度大小"}, "inputs": {"vector": {"nodeId": "get-velocity", "socket": "velocity"}}}, {"id": "speed-limit-branch", "type": "core/flow/branch", "position": {"x": 700, "y": 600}, "data": {"label": "速度限制分支"}, "inputs": {"condition": {"nodeId": "check-high-speed", "socket": "result"}}, "flows": {"true": {"nodeId": "limit-velocity", "socket": "flow"}}}, {"id": "limit-velocity", "type": "physics/setVelocity", "position": {"x": 900, "y": 600}, "data": {"label": "限制速度"}, "inputs": {"entity": {"nodeId": "get-player-var-2", "socket": "value"}, "velocity": {"nodeId": "calculate-limited-velocity", "socket": "result"}}}, {"id": "calculate-limited-velocity", "type": "math/vector/normalize", "position": {"x": 700, "y": 700}, "data": {"label": "计算限制速度"}, "inputs": {"vector": {"nodeId": "get-velocity", "socket": "velocity"}}}]}