# 新节点功能测试指南

## 概述

本文档提供了对新实现的15个视觉脚本节点（241-255）的测试指南，帮助验证节点功能是否正常工作。

## 🧪 测试环境准备

### 1. 启动开发环境
```bash
# 启动引擎开发服务器
cd engine
npm run dev

# 启动编辑器开发服务器
cd editor
npm run dev
```

### 2. 访问编辑器
打开浏览器访问：`http://localhost:3000`

## 📋 节点功能测试清单

### UI滑块节点测试 (241-244)

#### 测试用例1：创建滑块控件
**步骤**：
1. 在编辑器中拖拽"创建滑块"节点到画布
2. 设置输入参数：
   - 最小值：0
   - 最大值：100
   - 初始值：50
   - 步长：1
3. 连接执行流程
4. 运行脚本

**预期结果**：
- 页面上出现一个滑块控件
- 滑块初始值为50
- 滑块可以在0-100范围内拖动

#### 测试用例2：获取和设置滑块值
**步骤**：
1. 使用"创建滑块"节点创建滑块
2. 连接"获取滑块值"节点获取当前值
3. 使用"设置滑块值"节点设置新值为75
4. 再次获取滑块值验证

**预期结果**：
- 能够正确获取滑块的当前值
- 设置值后滑块位置更新到75
- 获取的新值为75

#### 测试用例3：滑块值变化事件
**步骤**：
1. 创建滑块控件
2. 连接"滑块值变化"事件节点
3. 连接打印节点输出当前值和之前值
4. 手动拖动滑块

**预期结果**：
- 拖动滑块时触发事件
- 控制台输出当前值和之前值
- 值变化正确反映滑块位置

### UI图像和面板节点测试 (245-249)

#### 测试用例4：创建图像控件
**步骤**：
1. 拖拽"创建图像"节点到画布
2. 设置图像源URL（可使用测试图片）
3. 设置宽度和高度
4. 运行脚本

**预期结果**：
- 页面上显示指定的图像
- 图像尺寸符合设置
- 图像样式正确应用

#### 测试用例5：动态更换图像纹理
**步骤**：
1. 创建图像控件
2. 使用"设置图像纹理"节点更换图像源
3. 验证图像更新

**预期结果**：
- 图像源成功更换
- 新图像正确显示

#### 测试用例6：面板容器管理
**步骤**：
1. 创建面板容器
2. 创建多个UI控件（按钮、图像等）
3. 使用"添加子控件"将控件添加到面板
4. 使用"移除子控件"移除某个控件

**预期结果**：
- 面板容器正确创建
- 子控件成功添加到面板中
- 移除操作正确执行

### 自定义事件系统测试 (250-252)

#### 测试用例7：自定义事件通信
**步骤**：
1. 使用"创建自定义事件"创建名为"testEvent"的事件
2. 在一个位置使用"监听事件"监听该事件
3. 在另一个位置使用"触发事件"触发该事件
4. 传递测试数据

**预期结果**：
- 自定义事件成功创建
- 事件监听器正确注册
- 触发事件时监听器被调用
- 数据正确传递

### 系统生命周期事件测试 (253-255)

#### 测试用例8：系统启动事件
**步骤**：
1. 添加"系统启动事件"节点
2. 连接打印节点输出启动信息
3. 运行脚本

**预期结果**：
- 脚本启动时触发事件
- 控制台输出启动信息

#### 测试用例9：系统更新事件
**步骤**：
1. 添加"系统更新事件"节点
2. 连接打印节点输出帧时间
3. 运行脚本

**预期结果**：
- 每帧都触发更新事件
- 控制台持续输出帧间隔时间

#### 测试用例10：系统销毁事件
**步骤**：
1. 添加"系统销毁事件"节点
2. 连接打印节点输出销毁信息
3. 运行脚本后停止

**预期结果**：
- 脚本停止时触发销毁事件
- 控制台输出销毁信息

## 🔍 综合测试场景

### 场景1：交互式UI面板
**目标**：创建一个包含滑块和图像的交互面板

**步骤**：
1. 创建面板容器
2. 创建滑块控件
3. 创建图像控件
4. 将滑块和图像添加到面板
5. 监听滑块值变化
6. 根据滑块值动态调整图像透明度

### 场景2：事件驱动的应用
**目标**：使用自定义事件实现模块间通信

**步骤**：
1. 创建"数据更新"自定义事件
2. 在数据模块中触发事件
3. 在UI模块中监听事件
4. 根据事件数据更新UI显示

### 场景3：完整的应用生命周期
**目标**：演示完整的应用生命周期管理

**步骤**：
1. 系统启动时初始化UI
2. 系统更新时处理用户交互
3. 系统销毁时清理资源

## 📊 测试结果记录

### 测试通过标准
- [ ] 所有节点能够正确创建和连接
- [ ] 节点执行无错误
- [ ] 输出结果符合预期
- [ ] 事件正确触发和处理
- [ ] 内存无泄漏
- [ ] 性能表现良好

### 常见问题排查

#### 问题1：节点无法找到
**解决方案**：
- 检查节点是否正确注册
- 验证导入路径是否正确
- 确认构建是否成功

#### 问题2：事件不触发
**解决方案**：
- 检查事件监听器是否正确设置
- 验证事件名称是否匹配
- 确认事件对象是否有效

#### 问题3：UI控件不显示
**解决方案**：
- 检查DOM元素是否正确创建
- 验证样式设置是否正确
- 确认元素是否添加到页面

## 🎯 性能测试

### 内存使用测试
- 创建大量UI控件
- 监控内存使用情况
- 验证垃圾回收是否正常

### 事件性能测试
- 创建大量事件监听器
- 频繁触发事件
- 测量响应时间

### 渲染性能测试
- 创建复杂的UI布局
- 测量渲染帧率
- 验证动画流畅性

## 📝 测试报告模板

```
测试日期：____
测试人员：____
测试环境：____

节点测试结果：
- UI滑块节点：✅/❌
- UI图像面板节点：✅/❌
- 自定义事件节点：✅/❌
- 系统生命周期节点：✅/❌

发现问题：
1. ____
2. ____

建议改进：
1. ____
2. ____
```

## 🚀 下一步测试计划

1. **集成测试**：与现有节点的兼容性测试
2. **压力测试**：大规模节点网络的性能测试
3. **用户体验测试**：实际开发场景的可用性测试
4. **自动化测试**：编写单元测试和集成测试用例
