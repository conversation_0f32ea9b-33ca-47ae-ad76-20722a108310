/**
 * 视觉脚本WebSocket节点
 * 提供WebSocket实时通信功能
 */
import { AsyncNode, AsyncNodeOptions } from '../nodes/AsyncNode';
import { EventNode, EventNodeOptions } from '../nodes/EventNode';
import { FlowNode, FlowNodeOptions } from '../nodes/FlowNode';
import { NodeCategory, SocketDirection, SocketType, NodeOptions } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';

/**
 * WebSocket连接管理器
 */
class WebSocketManager {
  private static instance: WebSocketManager;
  private connections: Map<string, WebSocket> = new Map();

  public static getInstance(): WebSocketManager {
    if (!WebSocketManager.instance) {
      WebSocketManager.instance = new WebSocketManager();
    }
    return WebSocketManager.instance;
  }

  public connect(url: string, protocols?: string[]): Promise<WebSocket> {
    return new Promise((resolve, reject) => {
      try {
        const ws = new WebSocket(url, protocols);
        
        ws.onopen = () => {
          this.connections.set(url, ws);
          resolve(ws);
        };

        ws.onerror = (error) => {
          reject(new Error(`WebSocket连接失败: ${error}`));
        };

        ws.onclose = () => {
          this.connections.delete(url);
        };
      } catch (error) {
        reject(error);
      }
    });
  }

  public getConnection(url: string): WebSocket | undefined {
    return this.connections.get(url);
  }

  public disconnect(url: string): void {
    const ws = this.connections.get(url);
    if (ws) {
      ws.close();
      this.connections.delete(url);
    }
  }

  public disconnectAll(): void {
    for (const [url, ws] of this.connections.entries()) {
      ws.close();
    }
    this.connections.clear();
  }
}

/**
 * WebSocket连接节点
 * 建立WebSocket连接
 */
export class WebSocketConnectNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'url',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: 'WebSocket服务器URL',
      defaultValue: 'ws://localhost:8080'
    });

    this.addInput({
      name: 'protocols',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '协议列表',
      optional: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'connected',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '连接成功流程'
    });

    this.addOutput({
      name: 'failed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '连接失败流程'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'connection',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: 'WebSocket连接对象'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  /**
   * 执行异步操作
   * @param inputs 输入值
   * @returns Promise对象
   */
  protected async executeAsync(inputs: Record<string, any>): Promise<any> {
    const url = inputs.url as string;
    const protocols = inputs.protocols as string[];

    // 验证URL
    if (!url) {
      this.setOutputValue('error', 'WebSocket URL不能为空');
      this.triggerFlow('failed');
      return false;
    }

    try {
      const manager = WebSocketManager.getInstance();
      const ws = await manager.connect(url, protocols);

      // 设置输出值
      this.setOutputValue('connection', ws);

      // 触发连接成功流程
      this.triggerFlow('connected');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      console.error('WebSocket连接失败:', errorMessage);
      
      // 设置错误输出
      this.setOutputValue('error', errorMessage);
      
      // 触发连接失败流程
      this.triggerFlow('failed');
      return false;
    }
  }
}

/**
 * WebSocket发送节点
 * 通过WebSocket发送消息
 */
export class WebSocketSendNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const flowOptions: FlowNodeOptions = {
      ...options,
      inputFlowName: 'flow',
      outputFlowNames: ['success', 'fail']
    };
    super(flowOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加输入数据插槽
    this.addInput({
      name: 'url',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: 'WebSocket服务器URL',
      defaultValue: 'ws://localhost:8080'
    });

    this.addInput({
      name: 'message',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.INPUT,
      description: '要发送的消息'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const url = this.getInputValue('url') as string;
    const message = this.getInputValue('message');

    // 验证输入
    if (!url) {
      this.setOutputValue('error', 'WebSocket URL不能为空');
      this.triggerFlow('fail');
      return false;
    }

    if (message === undefined || message === null) {
      this.setOutputValue('error', '消息不能为空');
      this.triggerFlow('fail');
      return false;
    }

    try {
      const manager = WebSocketManager.getInstance();
      const ws = manager.getConnection(url);

      if (!ws) {
        this.setOutputValue('error', 'WebSocket连接不存在，请先建立连接');
        this.triggerFlow('fail');
        return false;
      }

      if (ws.readyState !== WebSocket.OPEN) {
        this.setOutputValue('error', 'WebSocket连接未打开');
        this.triggerFlow('fail');
        return false;
      }

      // 发送消息
      const messageStr = typeof message === 'string' ? message : JSON.stringify(message);
      ws.send(messageStr);

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      console.error('WebSocket发送消息失败:', errorMessage);
      
      // 设置错误输出
      this.setOutputValue('error', errorMessage);
      
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * WebSocket消息监听节点
 * 监听WebSocket消息
 */
export class WebSocketOnMessageNode extends EventNode {
  private messageListener?: (event: MessageEvent) => void;

  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const eventOptions: EventNodeOptions = {
      ...options,
      outputFlowName: 'onMessage'
    };
    super(eventOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加输入数据插槽
    this.addInput({
      name: 'url',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: 'WebSocket服务器URL',
      defaultValue: 'ws://localhost:8080'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'message',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.OUTPUT,
      description: '接收到的消息'
    });

    this.addOutput({
      name: 'rawData',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '原始消息数据'
    });
  }

  /**
   * 初始化节点
   */
  public initialize(): void {
    super.initialize();
    this.setupMessageListener();
  }

  /**
   * 设置消息监听器
   */
  private setupMessageListener(): void {
    const url = this.getInputValue('url') as string;

    if (!url) {
      console.warn('WebSocket URL为空，无法设置消息监听器');
      return;
    }

    const manager = WebSocketManager.getInstance();
    const ws = manager.getConnection(url);

    if (!ws) {
      console.warn('WebSocket连接不存在，无法设置消息监听器');
      return;
    }

    // 移除旧的监听器
    if (this.messageListener) {
      ws.removeEventListener('message', this.messageListener);
    }

    // 创建新的监听器
    this.messageListener = (event: MessageEvent) => {
      const rawData = event.data;
      let parsedMessage: any;

      try {
        parsedMessage = JSON.parse(rawData);
      } catch {
        parsedMessage = rawData;
      }

      // 设置输出值
      this.setOutputValue('message', parsedMessage);
      this.setOutputValue('rawData', rawData);

      // 触发消息事件
      this.triggerFlow('onMessage');
    };

    // 添加监听器
    ws.addEventListener('message', this.messageListener);
  }

  /**
   * 销毁节点
   */
  public dispose(): void {
    const url = this.getInputValue('url') as string;

    if (url && this.messageListener) {
      const manager = WebSocketManager.getInstance();
      const ws = manager.getConnection(url);

      if (ws) {
        ws.removeEventListener('message', this.messageListener);
      }
    }

    super.dispose();
  }
}

/**
 * WebSocket断开节点
 * 断开WebSocket连接
 */
export class WebSocketDisconnectNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const flowOptions: FlowNodeOptions = {
      ...options,
      inputFlowName: 'flow',
      outputFlowNames: ['disconnected']
    };
    super(flowOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加输入数据插槽
    this.addInput({
      name: 'url',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: 'WebSocket服务器URL',
      defaultValue: 'ws://localhost:8080'
    });

    this.addInput({
      name: 'code',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '关闭代码',
      defaultValue: 1000,
      optional: true
    });

    this.addInput({
      name: 'reason',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '关闭原因',
      defaultValue: '正常关闭',
      optional: true
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const url = this.getInputValue('url') as string;
    const code = this.getInputValue('code') as number || 1000;
    const reason = this.getInputValue('reason') as string || '正常关闭';

    // 验证URL
    if (!url) {
      console.warn('WebSocket URL为空，无法断开连接');
      this.triggerFlow('disconnected');
      return false;
    }

    try {
      const manager = WebSocketManager.getInstance();
      const ws = manager.getConnection(url);

      if (ws) {
        ws.close(code, reason);
        manager.disconnect(url);
      }

      // 触发断开流程
      this.triggerFlow('disconnected');
      return true;
    } catch (error) {
      console.error('WebSocket断开连接失败:', error);
      // 即使出错也触发断开流程
      this.triggerFlow('disconnected');
      return false;
    }
  }
}

/**
 * 注册WebSocket节点
 * @param registry 节点注册表
 */
export function registerWebSocketNodes(registry: NodeRegistry): void {
  // 注册WebSocket连接节点
  registry.registerNodeType({
    type: 'network/websocket/connect',
    category: NodeCategory.NETWORK,
    constructor: WebSocketConnectNode,
    label: 'WebSocket连接',
    description: '建立WebSocket连接',
    icon: 'websocket',
    color: '#00BCD4',
    tags: ['network', 'websocket', 'connect']
  });

  // 注册WebSocket发送节点
  registry.registerNodeType({
    type: 'network/websocket/send',
    category: NodeCategory.NETWORK,
    constructor: WebSocketSendNode,
    label: 'WebSocket发送',
    description: '通过WebSocket发送消息',
    icon: 'send',
    color: '#00BCD4',
    tags: ['network', 'websocket', 'send']
  });

  // 注册WebSocket消息监听节点
  registry.registerNodeType({
    type: 'network/websocket/onMessage',
    category: NodeCategory.NETWORK,
    constructor: WebSocketOnMessageNode,
    label: 'WebSocket消息',
    description: '监听WebSocket消息',
    icon: 'message',
    color: '#00BCD4',
    tags: ['network', 'websocket', 'message', 'event']
  });

  // 注册WebSocket断开节点
  registry.registerNodeType({
    type: 'network/websocket/disconnect',
    category: NodeCategory.NETWORK,
    constructor: WebSocketDisconnectNode,
    label: 'WebSocket断开',
    description: '断开WebSocket连接',
    icon: 'disconnect',
    color: '#00BCD4',
    tags: ['network', 'websocket', 'disconnect']
  });

  console.log('已注册所有WebSocket节点类型');
}
