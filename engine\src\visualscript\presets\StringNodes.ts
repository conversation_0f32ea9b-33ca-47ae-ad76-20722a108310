/**
 * 视觉脚本字符串节点
 * 提供字符串操作相关的节点
 */
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeCategory, NodeType, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry, NodeTypeInfo } from '../nodes/NodeRegistry';

/**
 * 字符串连接节点
 * 连接多个字符串
 */
export class StringConcatNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加第一个字符串输入
    this.addInput({
      name: 'str1',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '第一个字符串',
      defaultValue: ''
    });

    // 添加第二个字符串输入
    this.addInput({
      name: 'str2',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '第二个字符串',
      defaultValue: ''
    });

    // 添加分隔符输入
    this.addInput({
      name: 'separator',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '分隔符',
      defaultValue: ''
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '连接后的字符串'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const str1 = String(this.getInputValue('str1') || '');
    const str2 = String(this.getInputValue('str2') || '');
    const separator = String(this.getInputValue('separator') || '');

    // 连接字符串
    const result = str1 + separator + str2;

    // 设置输出值
    this.setOutputValue('result', result);

    return result;
  }
}

/**
 * 字符串分割节点
 * 按指定分隔符分割字符串
 */
export class StringSplitNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加字符串输入
    this.addInput({
      name: 'string',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '要分割的字符串',
      defaultValue: ''
    });

    // 添加分隔符输入
    this.addInput({
      name: 'separator',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '分隔符',
      defaultValue: ','
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'array',
      description: '分割后的字符串数组'
    });

    // 添加长度输出
    this.addOutput({
      name: 'length',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '分割后数组的长度'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const string = String(this.getInputValue('string') || '');
    const separator = String(this.getInputValue('separator') || ',');

    // 分割字符串
    const result = string.split(separator);

    // 设置输出值
    this.setOutputValue('result', result);
    this.setOutputValue('length', result.length);

    return result;
  }
}

/**
 * 子字符串节点
 * 提取字符串的子串
 */
export class StringSubstringNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加字符串输入
    this.addInput({
      name: 'string',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '源字符串',
      defaultValue: ''
    });

    // 添加开始位置输入
    this.addInput({
      name: 'start',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '开始位置',
      defaultValue: 0
    });

    // 添加长度输入
    this.addInput({
      name: 'length',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '子串长度（可选）',
      defaultValue: -1,
      optional: true
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '子字符串'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const str = String(this.getInputValue('string') || '');
    const start = Number(this.getInputValue('start') || 0);
    const length = Number(this.getInputValue('length') || -1);

    // 提取子字符串
    let result: string;
    if (length >= 0) {
      result = str.substring(start, start + length);
    } else {
      result = str.substring(start);
    }

    // 设置输出值
    this.setOutputValue('result', result);

    return result;
  }
}

/**
 * 查找位置节点
 * 查找子字符串在字符串中的位置
 */
export class StringIndexOfNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加字符串输入
    this.addInput({
      name: 'string',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '源字符串',
      defaultValue: ''
    });

    // 添加搜索字符串输入
    this.addInput({
      name: 'searchString',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '要查找的子字符串',
      defaultValue: ''
    });

    // 添加开始位置输入
    this.addInput({
      name: 'startIndex',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '开始搜索位置',
      defaultValue: 0,
      optional: true
    });

    // 添加位置输出
    this.addOutput({
      name: 'index',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '找到的位置（-1表示未找到）'
    });

    // 添加是否找到输出
    this.addOutput({
      name: 'found',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否找到'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const str = String(this.getInputValue('string') || '');
    const searchString = String(this.getInputValue('searchString') || '');
    const startIndex = Number(this.getInputValue('startIndex') || 0);

    // 查找位置
    const index = str.indexOf(searchString, startIndex);
    const found = index !== -1;

    // 设置输出值
    this.setOutputValue('index', index);
    this.setOutputValue('found', found);

    return { index, found };
  }
}

/**
 * 字符串替换节点
 * 替换字符串中的指定内容
 */
export class StringReplaceNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加字符串输入
    this.addInput({
      name: 'string',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '源字符串',
      defaultValue: ''
    });

    // 添加搜索字符串输入
    this.addInput({
      name: 'searchValue',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '要替换的内容',
      defaultValue: ''
    });

    // 添加替换字符串输入
    this.addInput({
      name: 'replaceValue',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '替换为的内容',
      defaultValue: ''
    });

    // 添加是否全部替换输入
    this.addInput({
      name: 'replaceAll',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'boolean',
      description: '是否替换所有匹配项',
      defaultValue: false,
      optional: true
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '替换后的字符串'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const str = String(this.getInputValue('string') || '');
    const searchValue = String(this.getInputValue('searchValue') || '');
    const replaceValue = String(this.getInputValue('replaceValue') || '');
    const replaceAll = Boolean(this.getInputValue('replaceAll') || false);

    // 执行替换
    let result: string;
    if (replaceAll) {
      result = str.split(searchValue).join(replaceValue);
    } else {
      result = str.replace(searchValue, replaceValue);
    }

    // 设置输出值
    this.setOutputValue('result', result);

    return result;
  }
}

/**
 * 转大写节点
 * 将字符串转换为大写
 */
export class StringToUpperCaseNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加字符串输入
    this.addInput({
      name: 'string',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '源字符串',
      defaultValue: ''
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '大写字符串'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const str = String(this.getInputValue('string') || '');

    // 转换为大写
    const result = str.toUpperCase();

    // 设置输出值
    this.setOutputValue('result', result);

    return result;
  }
}

/**
 * 转小写节点
 * 将字符串转换为小写
 */
export class StringToLowerCaseNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加字符串输入
    this.addInput({
      name: 'string',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '源字符串',
      defaultValue: ''
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '小写字符串'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const str = String(this.getInputValue('string') || '');

    // 转换为小写
    const result = str.toLowerCase();

    // 设置输出值
    this.setOutputValue('result', result);

    return result;
  }
}

/**
 * 去除空格节点
 * 去除字符串首尾空格
 */
export class StringTrimNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加字符串输入
    this.addInput({
      name: 'string',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '源字符串',
      defaultValue: ''
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '去除空格后的字符串'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const str = String(this.getInputValue('string') || '');

    // 去除首尾空格
    const result = str.trim();

    // 设置输出值
    this.setOutputValue('result', result);

    return result;
  }
}

/**
 * 字符串长度节点
 * 获取字符串的长度
 */
export class StringLengthNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加字符串输入
    this.addInput({
      name: 'string',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '源字符串',
      defaultValue: ''
    });

    // 添加长度输出
    this.addOutput({
      name: 'length',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '字符串长度'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const str = String(this.getInputValue('string') || '');

    // 获取长度
    const length = str.length;

    // 设置输出值
    this.setOutputValue('length', length);

    return length;
  }
}

/**
 * 包含检查节点
 * 检查字符串是否包含指定子串
 */
export class StringContainsNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加字符串输入
    this.addInput({
      name: 'string',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '源字符串',
      defaultValue: ''
    });

    // 添加搜索字符串输入
    this.addInput({
      name: 'searchString',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '要查找的子字符串',
      defaultValue: ''
    });

    // 添加是否包含输出
    this.addOutput({
      name: 'contains',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否包含'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const str = String(this.getInputValue('string') || '');
    const searchString = String(this.getInputValue('searchString') || '');

    // 检查是否包含
    const contains = str.includes(searchString);

    // 设置输出值
    this.setOutputValue('contains', contains);

    return contains;
  }
}

/**
 * 注册字符串节点
 * @param registry 节点注册表
 */
export function registerStringNodes(registry: NodeRegistry): void {
  // 注册字符串连接节点
  registry.registerNodeType({
    type: 'string/concat',
    category: NodeCategory.STRING,
    constructor: StringConcatNode,
    label: '字符串连接',
    description: '连接多个字符串',
    icon: 'link',
    color: '#52C41A',
    tags: ['string', 'concat', 'join']
  });

  // 注册字符串分割节点
  registry.registerNodeType({
    type: 'string/split',
    category: NodeCategory.STRING,
    constructor: StringSplitNode,
    label: '字符串分割',
    description: '按指定分隔符分割字符串',
    icon: 'scissor',
    color: '#52C41A',
    tags: ['string', 'split', 'array']
  });

  // 注册子字符串节点
  registry.registerNodeType({
    type: 'string/substring',
    category: NodeCategory.STRING,
    constructor: StringSubstringNode,
    label: '子字符串',
    description: '提取字符串的子串',
    icon: 'cut',
    color: '#52C41A',
    tags: ['string', 'substring', 'extract']
  });

  // 注册查找位置节点
  registry.registerNodeType({
    type: 'string/indexOf',
    category: NodeCategory.STRING,
    constructor: StringIndexOfNode,
    label: '查找位置',
    description: '查找子字符串在字符串中的位置',
    icon: 'search',
    color: '#52C41A',
    tags: ['string', 'indexOf', 'search']
  });

  // 注册字符串替换节点
  registry.registerNodeType({
    type: 'string/replace',
    category: NodeCategory.STRING,
    constructor: StringReplaceNode,
    label: '字符串替换',
    description: '替换字符串中的指定内容',
    icon: 'swap',
    color: '#52C41A',
    tags: ['string', 'replace', 'substitute']
  });

  // 注册转大写节点
  registry.registerNodeType({
    type: 'string/toUpperCase',
    category: NodeCategory.STRING,
    constructor: StringToUpperCaseNode,
    label: '转大写',
    description: '将字符串转换为大写',
    icon: 'font-size',
    color: '#52C41A',
    tags: ['string', 'uppercase', 'case']
  });

  // 注册转小写节点
  registry.registerNodeType({
    type: 'string/toLowerCase',
    category: NodeCategory.STRING,
    constructor: StringToLowerCaseNode,
    label: '转小写',
    description: '将字符串转换为小写',
    icon: 'font-size',
    color: '#52C41A',
    tags: ['string', 'lowercase', 'case']
  });

  // 注册去除空格节点
  registry.registerNodeType({
    type: 'string/trim',
    category: NodeCategory.STRING,
    constructor: StringTrimNode,
    label: '去除空格',
    description: '去除字符串首尾空格',
    icon: 'clear',
    color: '#52C41A',
    tags: ['string', 'trim', 'whitespace']
  });

  // 注册字符串长度节点
  registry.registerNodeType({
    type: 'string/length',
    category: NodeCategory.STRING,
    constructor: StringLengthNode,
    label: '字符串长度',
    description: '获取字符串的长度',
    icon: 'number',
    color: '#52C41A',
    tags: ['string', 'length', 'count']
  });

  // 注册包含检查节点
  registry.registerNodeType({
    type: 'string/contains',
    category: NodeCategory.STRING,
    constructor: StringContainsNode,
    label: '包含检查',
    description: '检查字符串是否包含指定子串',
    icon: 'check',
    color: '#52C41A',
    tags: ['string', 'contains', 'includes']
  });
}
