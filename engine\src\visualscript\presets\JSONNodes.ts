/**
 * 视觉脚本JSON节点
 * 提供JSON数据处理功能，如解析和序列化
 */
import { FlowNode, FlowNodeOptions } from '../nodes/FlowNode';
import { NodeCategory, SocketDirection, SocketType, NodeOptions } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';

/**
 * JSON解析节点
 * 解析JSON字符串为对象
 */
export class JSONParseNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const flowOptions: FlowNodeOptions = {
      ...options,
      inputFlowName: 'flow',
      outputFlowNames: ['success', 'fail']
    };
    super(flowOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加输入数据插槽
    this.addInput({
      name: 'jsonString',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: 'JSON字符串',
      defaultValue: '{"key": "value"}'
    });

    this.addInput({
      name: 'reviver',
      type: SocketType.DATA,
      dataType: 'function',
      direction: SocketDirection.INPUT,
      description: '转换函数（可选）',
      optional: true
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.OUTPUT,
      description: '解析结果对象'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });

    this.addOutput({
      name: 'isValid',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否为有效JSON'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const jsonString = this.getInputValue('jsonString') as string;
    const reviver = this.getInputValue('reviver') as ((key: string, value: any) => any) | undefined;

    // 验证输入
    if (typeof jsonString !== 'string') {
      this.setOutputValue('error', '输入必须是字符串类型');
      this.setOutputValue('isValid', false);
      this.triggerFlow('fail');
      return null;
    }

    if (jsonString.trim() === '') {
      this.setOutputValue('error', 'JSON字符串不能为空');
      this.setOutputValue('isValid', false);
      this.triggerFlow('fail');
      return null;
    }

    try {
      // 解析JSON
      const result = JSON.parse(jsonString, reviver);

      // 设置输出值
      this.setOutputValue('result', result);
      this.setOutputValue('isValid', true);

      // 触发成功流程
      this.triggerFlow('success');
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知解析错误';
      console.error('JSON解析失败:', errorMessage);

      // 设置错误输出
      this.setOutputValue('error', errorMessage);
      this.setOutputValue('isValid', false);
      this.setOutputValue('result', null);

      // 触发失败流程
      this.triggerFlow('fail');
      return null;
    }
  }
}

/**
 * JSON序列化节点
 * 将对象序列化为JSON字符串
 */
export class JSONStringifyNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    const flowOptions: FlowNodeOptions = {
      ...options,
      inputFlowName: 'flow',
      outputFlowNames: ['success', 'fail']
    };
    super(flowOptions);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加输入数据插槽
    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.INPUT,
      description: '要序列化的值',
      defaultValue: { key: 'value' }
    });

    this.addInput({
      name: 'replacer',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.INPUT,
      description: '替换函数或属性数组（可选）',
      optional: true
    });

    this.addInput({
      name: 'space',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.INPUT,
      description: '缩进空格数或字符串（可选）',
      optional: true
    });

    this.addInput({
      name: 'prettyPrint',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否格式化输出',
      defaultValue: false,
      optional: true
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: 'JSON字符串'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });

    this.addOutput({
      name: 'length',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '字符串长度'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const value = this.getInputValue('value');
    const replacer = this.getInputValue('replacer');
    const space = this.getInputValue('space');
    const prettyPrint = this.getInputValue('prettyPrint') as boolean;

    try {
      // 确定缩进参数
      let spaceParam = space;
      if (prettyPrint && spaceParam === undefined) {
        spaceParam = 2; // 默认缩进2个空格
      }

      // 序列化JSON
      const result = JSON.stringify(value, replacer, spaceParam);

      // 设置输出值
      this.setOutputValue('result', result);
      this.setOutputValue('length', result.length);

      // 触发成功流程
      this.triggerFlow('success');
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知序列化错误';
      console.error('JSON序列化失败:', errorMessage);

      // 设置错误输出
      this.setOutputValue('error', errorMessage);
      this.setOutputValue('result', '');
      this.setOutputValue('length', 0);

      // 触发失败流程
      this.triggerFlow('fail');
      return '';
    }
  }
}

/**
 * 注册JSON节点
 * @param registry 节点注册表
 */
export function registerJSONNodes(registry: NodeRegistry): void {
  // 注册JSON解析节点
  registry.registerNodeType({
    type: 'data/json/parse',
    category: NodeCategory.DATA,
    constructor: JSONParseNode,
    label: 'JSON解析',
    description: '解析JSON字符串为对象',
    icon: 'json',
    color: '#FF9800',
    tags: ['data', 'json', 'parse', 'string']
  });

  // 注册JSON序列化节点
  registry.registerNodeType({
    type: 'data/json/stringify',
    category: NodeCategory.DATA,
    constructor: JSONStringifyNode,
    label: 'JSON序列化',
    description: '将对象序列化为JSON字符串',
    icon: 'json',
    color: '#FF9800',
    tags: ['data', 'json', 'stringify', 'object']
  });

  console.log('已注册所有JSON节点类型');
}
