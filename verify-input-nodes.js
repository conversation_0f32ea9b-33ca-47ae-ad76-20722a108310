// 验证输入处理节点是否正确实现和注册
const { NodeRegistry } = require('./engine/dist/visualscript/NodeRegistry');
const { registerInputNodes } = require('./engine/dist/visualscript/presets/InputNodes');

console.log('=== 验证输入处理节点实现 ===\n');

// 创建节点注册表
const registry = new NodeRegistry();

try {
  // 注册输入节点
  registerInputNodes(registry);
  console.log('✅ 输入节点注册成功\n');

  // 要验证的新输入处理节点列表 (152-165)
  const newInputNodes = [
    { type: 'input/keyboard/onKeyPress', name: '按键事件' },
    { type: 'input/mouse/getPosition', name: '鼠标位置' },
    { type: 'input/mouse/isButtonDown', name: '鼠标按下' },
    { type: 'input/mouse/onClick', name: '鼠标点击' },
    { type: 'input/mouse/onMove', name: '鼠标移动' },
    { type: 'input/mouse/onWheel', name: '鼠标滚轮' },
    { type: 'input/touch/getTouchCount', name: '触摸点数量' },
    { type: 'input/touch/getTouchPosition', name: '触摸位置' },
    { type: 'input/touch/onTouchStart', name: '触摸开始' },
    { type: 'input/touch/onTouchEnd', name: '触摸结束' },
    { type: 'input/touch/onTouchMove', name: '触摸移动' },
    { type: 'input/gamepad/isConnected', name: '手柄连接' },
    { type: 'input/gamepad/getButtonState', name: '手柄按钮' },
    { type: 'input/gamepad/getAxisValue', name: '手柄摇杆' }
  ];

  console.log('验证新增输入处理节点 (152-165):\n');

  let successCount = 0;
  let failCount = 0;

  newInputNodes.forEach((nodeSpec, index) => {
    try {
      const nodeInfo = registry.getNodeTypeInfo(nodeSpec.type);
      
      if (nodeInfo) {
        console.log(`✅ ${152 + index}. ${nodeSpec.name} (${nodeSpec.type})`);
        console.log(`   标签: ${nodeInfo.label}`);
        console.log(`   描述: ${nodeInfo.description}`);
        console.log(`   分类: ${nodeInfo.category}`);
        
        // 尝试创建节点实例
        try {
          const node = new nodeInfo.constructor();
          console.log(`   实例化: ✅ 成功 (标题: ${node.title})`);
        } catch (createError) {
          console.log(`   实例化: ❌ 失败 - ${createError.message}`);
        }
        
        successCount++;
      } else {
        console.log(`❌ ${152 + index}. ${nodeSpec.name} (${nodeSpec.type}) - 未找到`);
        failCount++;
      }
      console.log('');
    } catch (error) {
      console.log(`❌ ${152 + index}. ${nodeSpec.name} (${nodeSpec.type}) - 错误: ${error.message}\n`);
      failCount++;
    }
  });

  console.log('=== 验证结果 ===');
  console.log(`总节点数: ${newInputNodes.length}`);
  console.log(`成功注册: ${successCount}`);
  console.log(`注册失败: ${failCount}`);
  console.log(`成功率: ${((successCount / newInputNodes.length) * 100).toFixed(1)}%`);

  if (successCount === newInputNodes.length) {
    console.log('\n🎉 所有新增输入处理节点都已成功实现和注册！');
  } else {
    console.log('\n⚠️  部分节点注册失败，请检查实现。');
  }

} catch (error) {
  console.error('❌ 节点注册失败:', error.message);
  console.error(error.stack);
}
