import { describe, it, expect, beforeEach } from 'vitest';
import { NodeRegistry } from '../../src/visualscript/NodeRegistry';
import { registerInputNodes } from '../../src/visualscript/presets/InputNodes';

describe('输入处理节点测试', () => {
  let registry: NodeRegistry;

  beforeEach(() => {
    registry = new NodeRegistry();
    registerInputNodes(registry);
  });

  describe('新增输入处理节点注册测试', () => {
    it('应该注册按键事件节点', () => {
      const nodeInfo = registry.getNodeTypeInfo('input/keyboard/onKeyPress');
      expect(nodeInfo).toBeDefined();
      expect(nodeInfo?.label).toBe('按键事件');
      expect(nodeInfo?.description).toBe('监听按键按下事件');
    });

    it('应该注册鼠标位置节点', () => {
      const nodeInfo = registry.getNodeTypeInfo('input/mouse/getPosition');
      expect(nodeInfo).toBeDefined();
      expect(nodeInfo?.label).toBe('鼠标位置');
      expect(nodeInfo?.description).toBe('获取鼠标当前位置');
    });

    it('应该注册鼠标按下节点', () => {
      const nodeInfo = registry.getNodeTypeInfo('input/mouse/isButtonDown');
      expect(nodeInfo).toBeDefined();
      expect(nodeInfo?.label).toBe('鼠标按下');
      expect(nodeInfo?.description).toBe('检查鼠标按钮是否被按下');
    });

    it('应该注册鼠标点击节点', () => {
      const nodeInfo = registry.getNodeTypeInfo('input/mouse/onClick');
      expect(nodeInfo).toBeDefined();
      expect(nodeInfo?.label).toBe('鼠标点击');
      expect(nodeInfo?.description).toBe('监听鼠标点击事件');
    });

    it('应该注册鼠标移动节点', () => {
      const nodeInfo = registry.getNodeTypeInfo('input/mouse/onMove');
      expect(nodeInfo).toBeDefined();
      expect(nodeInfo?.label).toBe('鼠标移动');
      expect(nodeInfo?.description).toBe('监听鼠标移动事件');
    });

    it('应该注册鼠标滚轮节点', () => {
      const nodeInfo = registry.getNodeTypeInfo('input/mouse/onWheel');
      expect(nodeInfo).toBeDefined();
      expect(nodeInfo?.label).toBe('鼠标滚轮');
      expect(nodeInfo?.description).toBe('监听鼠标滚轮事件');
    });

    it('应该注册触摸点数量节点', () => {
      const nodeInfo = registry.getNodeTypeInfo('input/touch/getTouchCount');
      expect(nodeInfo).toBeDefined();
      expect(nodeInfo?.label).toBe('触摸点数量');
      expect(nodeInfo?.description).toBe('获取当前触摸点数量');
    });

    it('应该注册触摸位置节点', () => {
      const nodeInfo = registry.getNodeTypeInfo('input/touch/getTouchPosition');
      expect(nodeInfo).toBeDefined();
      expect(nodeInfo?.label).toBe('触摸位置');
      expect(nodeInfo?.description).toBe('获取指定触摸点位置');
    });

    it('应该注册触摸开始节点', () => {
      const nodeInfo = registry.getNodeTypeInfo('input/touch/onTouchStart');
      expect(nodeInfo).toBeDefined();
      expect(nodeInfo?.label).toBe('触摸开始');
      expect(nodeInfo?.description).toBe('监听触摸开始事件');
    });

    it('应该注册触摸结束节点', () => {
      const nodeInfo = registry.getNodeTypeInfo('input/touch/onTouchEnd');
      expect(nodeInfo).toBeDefined();
      expect(nodeInfo?.label).toBe('触摸结束');
      expect(nodeInfo?.description).toBe('监听触摸结束事件');
    });

    it('应该注册触摸移动节点', () => {
      const nodeInfo = registry.getNodeTypeInfo('input/touch/onTouchMove');
      expect(nodeInfo).toBeDefined();
      expect(nodeInfo?.label).toBe('触摸移动');
      expect(nodeInfo?.description).toBe('监听触摸移动事件');
    });

    it('应该注册手柄连接节点', () => {
      const nodeInfo = registry.getNodeTypeInfo('input/gamepad/isConnected');
      expect(nodeInfo).toBeDefined();
      expect(nodeInfo?.label).toBe('手柄连接');
      expect(nodeInfo?.description).toBe('检查手柄是否连接');
    });

    it('应该注册手柄按钮节点', () => {
      const nodeInfo = registry.getNodeTypeInfo('input/gamepad/getButtonState');
      expect(nodeInfo).toBeDefined();
      expect(nodeInfo?.label).toBe('手柄按钮');
      expect(nodeInfo?.description).toBe('获取手柄按钮状态');
    });

    it('应该注册手柄摇杆节点', () => {
      const nodeInfo = registry.getNodeTypeInfo('input/gamepad/getAxisValue');
      expect(nodeInfo).toBeDefined();
      expect(nodeInfo?.label).toBe('手柄摇杆');
      expect(nodeInfo?.description).toBe('获取手柄摇杆轴值');
    });
  });

  describe('节点创建测试', () => {
    it('应该能够创建按键事件节点', () => {
      const nodeInfo = registry.getNodeTypeInfo('input/keyboard/onKeyPress');
      expect(nodeInfo).toBeDefined();
      
      if (nodeInfo) {
        const node = new nodeInfo.constructor();
        expect(node).toBeDefined();
        expect(node.title).toBe('按键事件');
      }
    });

    it('应该能够创建鼠标位置节点', () => {
      const nodeInfo = registry.getNodeTypeInfo('input/mouse/getPosition');
      expect(nodeInfo).toBeDefined();
      
      if (nodeInfo) {
        const node = new nodeInfo.constructor();
        expect(node).toBeDefined();
        expect(node.title).toBe('鼠标位置');
      }
    });

    it('应该能够创建手柄连接节点', () => {
      const nodeInfo = registry.getNodeTypeInfo('input/gamepad/isConnected');
      expect(nodeInfo).toBeDefined();
      
      if (nodeInfo) {
        const node = new nodeInfo.constructor();
        expect(node).toBeDefined();
        expect(node.title).toBe('手柄连接');
      }
    });
  });

  describe('节点数量统计', () => {
    it('应该注册所有14个新的输入处理节点', () => {
      const inputNodeTypes = [
        'input/keyboard/onKeyPress',
        'input/mouse/getPosition',
        'input/mouse/isButtonDown',
        'input/mouse/onClick',
        'input/mouse/onMove',
        'input/mouse/onWheel',
        'input/touch/getTouchCount',
        'input/touch/getTouchPosition',
        'input/touch/onTouchStart',
        'input/touch/onTouchEnd',
        'input/touch/onTouchMove',
        'input/gamepad/isConnected',
        'input/gamepad/getButtonState',
        'input/gamepad/getAxisValue'
      ];

      inputNodeTypes.forEach(nodeType => {
        const nodeInfo = registry.getNodeTypeInfo(nodeType);
        expect(nodeInfo).toBeDefined();
      });

      // 验证总数
      expect(inputNodeTypes.length).toBe(14);
    });
  });
});
