/**
 * 实体节点测试
 * 测试新实现的实体和组件操作节点 (166-180)
 */
import { describe, it, expect, beforeEach } from 'vitest';
import { NodeRegistry } from '../../src/visualscript/nodes/NodeRegistry';
import { NodeCategory } from '../../src/visualscript/nodes/Node';
import { registerEntityNodes } from '../../src/visualscript/presets/EntityNodes';

describe('实体和组件操作节点 (166-180)', () => {
  let registry: NodeRegistry;

  beforeEach(() => {
    registry = new NodeRegistry();
    registerEntityNodes(registry);
  });

  describe('实体操作节点', () => {
    const entityNodes = [
      'entity/create',
      'entity/destroy', 
      'entity/get',
      'entity/getName',
      'entity/setName',
      'entity/getTag',
      'entity/setTag',
      'entity/isActive',
      'entity/setActive',
      'entity/getParent',
      'entity/setParent',
      'entity/getChildren'
    ];

    it('应该正确注册所有实体节点', () => {
      for (const nodeType of entityNodes) {
        const nodeInfo = registry.getNodeTypeInfo(nodeType);
        expect(nodeInfo).toBeDefined();
        expect(nodeInfo?.type).toBe(nodeType);
        expect(nodeInfo?.category).toBe(NodeCategory.ENTITY);
      }
    });

    it('应该有正确的中文标签', () => {
      const expectedLabels = {
        'entity/create': '创建实体',
        'entity/destroy': '销毁实体',
        'entity/get': '获取实体',
        'entity/getName': '获取实体名称',
        'entity/setName': '设置实体名称',
        'entity/getTag': '获取实体标签',
        'entity/setTag': '设置实体标签',
        'entity/isActive': '实体激活状态',
        'entity/setActive': '设置激活状态',
        'entity/getParent': '获取父实体',
        'entity/setParent': '设置父实体',
        'entity/getChildren': '获取子实体'
      };

      for (const [nodeType, expectedLabel] of Object.entries(expectedLabels)) {
        const nodeInfo = registry.getNodeTypeInfo(nodeType);
        expect(nodeInfo?.label).toBe(expectedLabel);
      }
    });
  });

  describe('组件操作节点', () => {
    const componentNodes = [
      'component/add',
      'component/remove',
      'component/get'
    ];

    it('应该正确注册所有组件节点', () => {
      for (const nodeType of componentNodes) {
        const nodeInfo = registry.getNodeTypeInfo(nodeType);
        expect(nodeInfo).toBeDefined();
        expect(nodeInfo?.type).toBe(nodeType);
        expect(nodeInfo?.category).toBe(NodeCategory.ENTITY);
      }
    });

    it('应该有正确的中文标签', () => {
      const expectedLabels = {
        'component/add': '添加组件',
        'component/remove': '移除组件',
        'component/get': '获取组件'
      };

      for (const [nodeType, expectedLabel] of Object.entries(expectedLabels)) {
        const nodeInfo = registry.getNodeTypeInfo(nodeType);
        expect(nodeInfo?.label).toBe(expectedLabel);
      }
    });
  });

  describe('节点分类', () => {
    it('所有新节点都应该在实体分类中', () => {
      const entityNodes = registry.getNodeTypesByCategory(NodeCategory.ENTITY);
      const expectedNodes = [
        'entity/create', 'entity/destroy', 'entity/get',
        'entity/getName', 'entity/setName', 'entity/getTag', 'entity/setTag',
        'entity/isActive', 'entity/setActive', 'entity/getParent', 
        'entity/setParent', 'entity/getChildren',
        'component/add', 'component/remove', 'component/get'
      ];

      for (const nodeType of expectedNodes) {
        const found = entityNodes.some(node => node.type === nodeType);
        expect(found).toBe(true);
      }
    });
  });

  describe('节点标签', () => {
    it('实体节点应该有entity标签', () => {
      const entityTagNodes = registry.getNodeTypesByTag('entity');
      expect(entityTagNodes.length).toBeGreaterThan(0);
      
      const entityNodeTypes = ['entity/create', 'entity/destroy', 'entity/get'];
      for (const nodeType of entityNodeTypes) {
        const found = entityTagNodes.some(node => node.type === nodeType);
        expect(found).toBe(true);
      }
    });

    it('组件节点应该有component标签', () => {
      const componentTagNodes = registry.getNodeTypesByTag('component');
      expect(componentTagNodes.length).toBeGreaterThan(0);
      
      const componentNodeTypes = ['component/add', 'component/remove', 'component/get'];
      for (const nodeType of componentNodeTypes) {
        const found = componentTagNodes.some(node => node.type === nodeType);
        expect(found).toBe(true);
      }
    });
  });

  describe('节点搜索', () => {
    it('应该能够搜索实体相关节点', () => {
      const searchResults = registry.searchNodeTypes('实体');
      expect(searchResults.length).toBeGreaterThan(0);
      
      // 验证搜索结果包含实体节点
      const entityNodeFound = searchResults.some(node => node.type.includes('entity'));
      expect(entityNodeFound).toBe(true);
    });

    it('应该能够搜索组件相关节点', () => {
      const searchResults = registry.searchNodeTypes('组件');
      expect(searchResults.length).toBeGreaterThan(0);
      
      // 验证搜索结果包含组件节点
      const componentNodeFound = searchResults.some(node => node.type.includes('component'));
      expect(componentNodeFound).toBe(true);
    });

    it('应该能够搜索创建相关节点', () => {
      const searchResults = registry.searchNodeTypes('创建');
      expect(searchResults.length).toBeGreaterThan(0);
      
      // 验证搜索结果包含创建节点
      const createNodeFound = searchResults.some(node => node.type === 'entity/create');
      expect(createNodeFound).toBe(true);
    });
  });

  describe('节点构造函数', () => {
    it('所有节点都应该有有效的构造函数', () => {
      const allNodes = [
        'entity/create', 'entity/destroy', 'entity/get',
        'entity/getName', 'entity/setName', 'entity/getTag', 'entity/setTag',
        'entity/isActive', 'entity/setActive', 'entity/getParent', 
        'entity/setParent', 'entity/getChildren',
        'component/add', 'component/remove', 'component/get'
      ];

      for (const nodeType of allNodes) {
        const constructor = registry.getNodeType(nodeType);
        expect(constructor).toBeDefined();
        expect(typeof constructor).toBe('function');
      }
    });
  });
});
