/**
 * 视觉脚本HTTP节点
 * 提供HTTP请求相关功能，如GET、POST、PUT、DELETE等
 */
import { AsyncNode, AsyncNodeOptions } from '../nodes/AsyncNode';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';

/**
 * HTTP GET请求节点
 * 发送HTTP GET请求
 */
export class HTTPGetNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'url',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '请求URL',
      defaultValue: 'https://api.example.com/data'
    });

    this.addInput({
      name: 'headers',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '请求头',
      defaultValue: {},
      optional: true
    });

    this.addInput({
      name: 'timeout',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '超时时间（毫秒）',
      defaultValue: 5000,
      optional: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '成功流程'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '失败流程'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'response',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.OUTPUT,
      description: '响应数据'
    });

    this.addOutput({
      name: 'statusCode',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '状态码'
    });

    this.addOutput({
      name: 'headers',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '响应头'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  /**
   * 执行异步操作
   * @param inputs 输入值
   * @returns Promise对象
   */
  protected async executeAsync(inputs: Record<string, any>): Promise<any> {
    const url = inputs.url as string;
    const headers = inputs.headers as Record<string, string> || {};
    const timeout = inputs.timeout as number || 5000;

    // 验证URL
    if (!url) {
      this.setOutputValue('error', 'URL不能为空');
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 使用fetch API发送HTTP GET请求
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      const response = await fetch(url, {
        method: 'GET',
        headers,
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      // 获取响应数据
      const responseData = await response.text();
      let parsedData: any;
      try {
        parsedData = JSON.parse(responseData);
      } catch {
        parsedData = responseData;
      }

      // 设置输出值
      this.setOutputValue('response', parsedData);
      this.setOutputValue('statusCode', response.status);
      this.setOutputValue('headers', Object.fromEntries(response.headers.entries()));

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      console.error('HTTP GET请求失败:', errorMessage);
      
      // 设置错误输出
      this.setOutputValue('error', errorMessage);
      
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * HTTP POST请求节点
 * 发送HTTP POST请求
 */
export class HTTPPostNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'url',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '请求URL',
      defaultValue: 'https://api.example.com/data'
    });

    this.addInput({
      name: 'body',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.INPUT,
      description: '请求体数据',
      optional: true
    });

    this.addInput({
      name: 'headers',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '请求头',
      defaultValue: { 'Content-Type': 'application/json' },
      optional: true
    });

    this.addInput({
      name: 'timeout',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '超时时间（毫秒）',
      defaultValue: 5000,
      optional: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '成功流程'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '失败流程'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'response',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.OUTPUT,
      description: '响应数据'
    });

    this.addOutput({
      name: 'statusCode',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '状态码'
    });

    this.addOutput({
      name: 'headers',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '响应头'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  /**
   * 执行异步操作
   * @param inputs 输入值
   * @returns Promise对象
   */
  protected async executeAsync(inputs: Record<string, any>): Promise<any> {
    const url = inputs.url as string;
    const body = inputs.body;
    const headers = inputs.headers as Record<string, string> || { 'Content-Type': 'application/json' };
    const timeout = inputs.timeout as number || 5000;

    // 验证URL
    if (!url) {
      this.setOutputValue('error', 'URL不能为空');
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 使用fetch API发送HTTP POST请求
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      const fetchOptions: RequestInit = {
        method: 'POST',
        headers,
        signal: controller.signal
      };

      // 添加请求体
      if (body !== undefined) {
        fetchOptions.body = typeof body === 'string' ? body : JSON.stringify(body);
      }

      const response = await fetch(url, fetchOptions);
      clearTimeout(timeoutId);

      // 获取响应数据
      const responseData = await response.text();
      let parsedData: any;
      try {
        parsedData = JSON.parse(responseData);
      } catch {
        parsedData = responseData;
      }

      // 设置输出值
      this.setOutputValue('response', parsedData);
      this.setOutputValue('statusCode', response.status);
      this.setOutputValue('headers', Object.fromEntries(response.headers.entries()));

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      console.error('HTTP POST请求失败:', errorMessage);
      
      // 设置错误输出
      this.setOutputValue('error', errorMessage);
      
      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * HTTP PUT请求节点
 * 发送HTTP PUT请求
 */
export class HTTPPutNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'url',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '请求URL',
      defaultValue: 'https://api.example.com/data'
    });

    this.addInput({
      name: 'body',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.INPUT,
      description: '请求体数据',
      optional: true
    });

    this.addInput({
      name: 'headers',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '请求头',
      defaultValue: { 'Content-Type': 'application/json' },
      optional: true
    });

    this.addInput({
      name: 'timeout',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '超时时间（毫秒）',
      defaultValue: 5000,
      optional: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '成功流程'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '失败流程'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'response',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.OUTPUT,
      description: '响应数据'
    });

    this.addOutput({
      name: 'statusCode',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '状态码'
    });

    this.addOutput({
      name: 'headers',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '响应头'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  /**
   * 执行异步操作
   * @param inputs 输入值
   * @returns Promise对象
   */
  protected async executeAsync(inputs: Record<string, any>): Promise<any> {
    const url = inputs.url as string;
    const body = inputs.body;
    const headers = inputs.headers as Record<string, string> || { 'Content-Type': 'application/json' };
    const timeout = inputs.timeout as number || 5000;

    // 验证URL
    if (!url) {
      this.setOutputValue('error', 'URL不能为空');
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 使用fetch API发送HTTP PUT请求
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      const fetchOptions: RequestInit = {
        method: 'PUT',
        headers,
        signal: controller.signal
      };

      // 添加请求体
      if (body !== undefined) {
        fetchOptions.body = typeof body === 'string' ? body : JSON.stringify(body);
      }

      const response = await fetch(url, fetchOptions);
      clearTimeout(timeoutId);

      // 获取响应数据
      const responseData = await response.text();
      let parsedData: any;
      try {
        parsedData = JSON.parse(responseData);
      } catch {
        parsedData = responseData;
      }

      // 设置输出值
      this.setOutputValue('response', parsedData);
      this.setOutputValue('statusCode', response.status);
      this.setOutputValue('headers', Object.fromEntries(response.headers.entries()));

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      console.error('HTTP PUT请求失败:', errorMessage);

      // 设置错误输出
      this.setOutputValue('error', errorMessage);

      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * HTTP DELETE请求节点
 * 发送HTTP DELETE请求
 */
export class HTTPDeleteNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'url',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '请求URL',
      defaultValue: 'https://api.example.com/data'
    });

    this.addInput({
      name: 'headers',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '请求头',
      defaultValue: {},
      optional: true
    });

    this.addInput({
      name: 'timeout',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '超时时间（毫秒）',
      defaultValue: 5000,
      optional: true
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '成功流程'
    });

    this.addOutput({
      name: 'fail',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '失败流程'
    });

    // 添加输出数据插槽
    this.addOutput({
      name: 'response',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.OUTPUT,
      description: '响应数据'
    });

    this.addOutput({
      name: 'statusCode',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '状态码'
    });

    this.addOutput({
      name: 'headers',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '响应头'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '错误信息'
    });
  }

  /**
   * 执行异步操作
   * @param inputs 输入值
   * @returns Promise对象
   */
  protected async executeAsync(inputs: Record<string, any>): Promise<any> {
    const url = inputs.url as string;
    const headers = inputs.headers as Record<string, string> || {};
    const timeout = inputs.timeout as number || 5000;

    // 验证URL
    if (!url) {
      this.setOutputValue('error', 'URL不能为空');
      this.triggerFlow('fail');
      return false;
    }

    try {
      // 使用fetch API发送HTTP DELETE请求
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      const response = await fetch(url, {
        method: 'DELETE',
        headers,
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      // 获取响应数据
      const responseData = await response.text();
      let parsedData: any;
      try {
        parsedData = JSON.parse(responseData);
      } catch {
        parsedData = responseData;
      }

      // 设置输出值
      this.setOutputValue('response', parsedData);
      this.setOutputValue('statusCode', response.status);
      this.setOutputValue('headers', Object.fromEntries(response.headers.entries()));

      // 触发成功流程
      this.triggerFlow('success');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      console.error('HTTP DELETE请求失败:', errorMessage);

      // 设置错误输出
      this.setOutputValue('error', errorMessage);

      // 触发失败流程
      this.triggerFlow('fail');
      return false;
    }
  }
}

/**
 * 注册HTTP节点
 * @param registry 节点注册表
 */
export function registerHTTPNodes(registry: NodeRegistry): void {
  // 注册HTTP GET请求节点
  registry.registerNodeType({
    type: 'network/http/get',
    category: NodeCategory.NETWORK,
    constructor: HTTPGetNode,
    label: 'HTTP GET请求',
    description: '发送HTTP GET请求',
    icon: 'http',
    color: '#00BCD4',
    tags: ['network', 'http', 'get', 'request']
  });

  // 注册HTTP POST请求节点
  registry.registerNodeType({
    type: 'network/http/post',
    category: NodeCategory.NETWORK,
    constructor: HTTPPostNode,
    label: 'HTTP POST请求',
    description: '发送HTTP POST请求',
    icon: 'http',
    color: '#00BCD4',
    tags: ['network', 'http', 'post', 'request']
  });

  // 注册HTTP PUT请求节点
  registry.registerNodeType({
    type: 'network/http/put',
    category: NodeCategory.NETWORK,
    constructor: HTTPPutNode,
    label: 'HTTP PUT请求',
    description: '发送HTTP PUT请求',
    icon: 'http',
    color: '#00BCD4',
    tags: ['network', 'http', 'put', 'request']
  });

  // 注册HTTP DELETE请求节点
  registry.registerNodeType({
    type: 'network/http/delete',
    category: NodeCategory.NETWORK,
    constructor: HTTPDeleteNode,
    label: 'HTTP DELETE请求',
    description: '发送HTTP DELETE请求',
    icon: 'http',
    color: '#00BCD4',
    tags: ['network', 'http', 'delete', 'request']
  });

  console.log('已注册所有HTTP节点类型');
}
