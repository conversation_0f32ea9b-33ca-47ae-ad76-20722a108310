/**
 * 视觉脚本渲染节点
 * 提供渲染系统相关的节点
 */
import type { Entity } from '../../core/Entity';
import { FlowNode } from '../nodes/FlowNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { Vector3 } from '../../math/Vector3';
import { MeshComponent } from '../../rendering/MeshComponent';
import { Camera } from '../../rendering/Camera';
import type { NodeOptions } from '../nodes/Node';
import * as THREE from 'three';

/**
 * 设置材质节点 (218)
 * 设置物体的渲染材质
 */
export class SetMaterialNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '要设置材质的实体'
    });

    this.addInput({
      name: 'material',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '要设置的材质对象'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功设置材质'
    });
  }

  public execute(): any {
    const entity = this.getInputValue('entity') as Entity;
    const material = this.getInputValue('material');

    if (!entity || !material) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      const meshComponent = entity.getComponent('MeshComponent') as MeshComponent;
      if (meshComponent) {
        meshComponent.setMaterial(material);
        this.setOutputValue('success', true);
        this.triggerFlow('flow');
        return true;
      }
      
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    } catch (error) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 获取材质节点 (219)
 * 获取物体的当前材质
 */
export class GetMaterialNode extends FunctionNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '要获取材质的实体'
    });

    this.addOutput({
      name: 'material',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '获取到的材质对象'
    });
  }

  public execute(): any {
    const entity = this.getInputValue('entity') as Entity;

    if (!entity) {
      this.setOutputValue('material', null);
      return null;
    }

    try {
      const meshComponent = entity.getComponent('MeshComponent') as MeshComponent;
      if (meshComponent) {
        // 简化实现，返回占位符材质对象
        const material = { type: 'material', entity: entity.id };
        this.setOutputValue('material', material);
        return material;
      }
      
      this.setOutputValue('material', null);
      return null;
    } catch (error) {
      this.setOutputValue('material', null);
      return null;
    }
  }
}

/**
 * 设置材质颜色节点 (220)
 * 设置材质的颜色属性
 */
export class SetMaterialColorNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '要设置颜色的实体'
    });

    this.addInput({
      name: 'color',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'RGB颜色值',
      defaultValue: { x: 1, y: 1, z: 1 }
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功设置颜色'
    });
  }

  public execute(): any {
    const entity = this.getInputValue('entity') as Entity;
    const color = this.getInputValue('color') as any;

    if (!entity || !color) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      // 简化实现
      console.log('设置材质颜色:', color);
      this.setOutputValue('success', true);
      this.triggerFlow('flow');
      return true;
    } catch (error) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 获取材质颜色节点 (221)
 * 获取材质的颜色属性
 */
export class GetMaterialColorNode extends FunctionNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '要获取颜色的实体'
    });

    this.addOutput({
      name: 'color',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '材质的颜色值'
    });
  }

  public execute(): any {
    const entity = this.getInputValue('entity') as Entity;

    if (!entity) {
      const defaultColor = { x: 1, y: 1, z: 1 };
      this.setOutputValue('color', defaultColor);
      return defaultColor;
    }

    try {
      // 简化实现，返回默认颜色
      const color = { x: 1, y: 1, z: 1 };
      this.setOutputValue('color', color);
      return color;
    } catch (error) {
      const defaultColor = { x: 1, y: 1, z: 1 };
      this.setOutputValue('color', defaultColor);
      return defaultColor;
    }
  }
}

/**
 * 设置材质纹理节点 (222)
 * 设置材质的纹理
 */
export class SetMaterialTextureNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '要设置纹理的实体'
    });

    this.addInput({
      name: 'texture',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '要设置的纹理对象'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功设置纹理'
    });
  }

  public execute(): any {
    const entity = this.getInputValue('entity') as Entity;
    const texture = this.getInputValue('texture');

    if (!entity || !texture) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      // 简化实现
      console.log('设置材质纹理:', texture);
      this.setOutputValue('success', true);
      this.triggerFlow('flow');
      return true;
    } catch (error) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 设置物体可见性节点 (223)
 * 设置物体是否可见
 */
export class SetVisibilityNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '要设置可见性的实体'
    });

    this.addInput({
      name: 'visible',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否可见',
      defaultValue: true
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功设置可见性'
    });
  }

  public execute(): any {
    const entity = this.getInputValue('entity') as Entity;
    const visible = this.getInputValue('visible') as boolean;

    if (!entity) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      const meshComponent = entity.getComponent('MeshComponent') as MeshComponent;
      if (meshComponent) {
        // 简化实现，因为MeshComponent可能没有setVisible方法
        console.log('设置物体可见性:', visible);
        this.setOutputValue('success', true);
        this.triggerFlow('flow');
        return true;
      }

      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    } catch (error) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 设置相机位置节点 (225)
 * 设置相机的位置
 */
export class SetCameraPositionNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'camera',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '要设置位置的相机对象'
    });

    this.addInput({
      name: 'position',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '新的相机位置'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功设置位置'
    });
  }

  public execute(): any {
    const camera = this.getInputValue('camera') as Camera;
    const position = this.getInputValue('position') as any;

    if (!camera || !position) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      // 获取相机的Three.js对象并设置位置
      const threeCamera = camera.getThreeCamera();
      if (threeCamera) {
        threeCamera.position.set(position.x || 0, position.y || 0, position.z || 0);
        threeCamera.updateMatrixWorld();
        this.setOutputValue('success', true);
        this.triggerFlow('flow');
        return true;
      }

      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    } catch (error) {
      console.error('设置相机位置失败:', error);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 获取相机位置节点 (226)
 * 获取相机的当前位置
 */
export class GetCameraPositionNode extends FunctionNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'camera',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '要获取位置的相机对象'
    });

    this.addOutput({
      name: 'position',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '相机的当前位置'
    });
  }

  public execute(): any {
    const camera = this.getInputValue('camera') as Camera;

    if (!camera) {
      const defaultPos = { x: 0, y: 0, z: 0 };
      this.setOutputValue('position', defaultPos);
      return defaultPos;
    }

    try {
      const position = camera.getPosition();
      const result = { x: position.x, y: position.y, z: position.z };
      this.setOutputValue('position', result);
      return result;
    } catch (error) {
      const defaultPos = { x: 0, y: 0, z: 0 };
      this.setOutputValue('position', defaultPos);
      return defaultPos;
    }
  }
}

/**
 * 相机朝向节点 (227)
 * 设置相机朝向目标
 */
export class CameraLookAtNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'camera',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '要设置朝向的相机对象'
    });

    this.addInput({
      name: 'target',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '相机朝向的目标位置'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功设置相机朝向'
    });
  }

  public execute(): any {
    const camera = this.getInputValue('camera') as Camera;
    const target = this.getInputValue('target') as any;

    if (!camera || !target) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      // 获取相机的Three.js对象并设置朝向
      const threeCamera = camera.getThreeCamera();
      if (threeCamera) {
        const targetVector = new THREE.Vector3(target.x || 0, target.y || 0, target.z || 0);
        threeCamera.lookAt(targetVector);
        threeCamera.updateMatrixWorld();
        this.setOutputValue('success', true);
        this.triggerFlow('flow');
        return true;
      }

      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    } catch (error) {
      console.error('设置相机朝向失败:', error);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 设置视野角度节点 (228)
 * 设置相机的视野角度
 */
export class SetCameraFOVNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'camera',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '要设置视野角度的相机对象'
    });

    this.addInput({
      name: 'fov',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '相机的视野角度（度）',
      defaultValue: 75
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功设置视野角度'
    });
  }

  public execute(): any {
    const camera = this.getInputValue('camera') as Camera;
    const fov = this.getInputValue('fov') as number;

    if (!camera || typeof fov !== 'number') {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      // 使用Camera类的setFov方法
      camera.setFov(fov);
      this.setOutputValue('success', true);
      this.triggerFlow('flow');
      return true;
    } catch (error) {
      console.error('设置相机视野角度失败:', error);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 获取视野角度节点 (229)
 * 获取相机的当前视野角度
 */
export class GetCameraFOVNode extends FunctionNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'camera',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '要获取视野角度的相机对象'
    });

    this.addOutput({
      name: 'fov',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '相机的视野角度'
    });
  }

  public execute(): any {
    const camera = this.getInputValue('camera') as Camera;

    if (!camera) {
      this.setOutputValue('fov', 75);
      return 75;
    }

    try {
      const fov = camera.getFov();
      this.setOutputValue('fov', fov);
      return fov;
    } catch (error) {
      console.error('获取相机视野角度失败:', error);
      this.setOutputValue('fov', 75);
      return 75;
    }
  }
}

/**
 * 设置相机缩放节点 (230)
 * 设置相机的缩放级别
 */
export class SetCameraZoomNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'camera',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '要设置缩放的相机对象'
    });

    this.addInput({
      name: 'zoom',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '缩放级别',
      defaultValue: 1.0
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功设置缩放'
    });
  }

  public execute(): any {
    const camera = this.getInputValue('camera') as Camera;
    const zoom = this.getInputValue('zoom') as number;

    if (!camera || typeof zoom !== 'number') {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      // 使用Camera类的setZoom方法
      camera.setZoom(zoom);
      this.setOutputValue('success', true);
      this.triggerFlow('flow');
      return true;
    } catch (error) {
      console.error('设置相机缩放失败:', error);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 注册渲染节点
 * @param registry 节点注册表
 */
export function registerRenderNodes(registry: NodeRegistry): void {
  // 注册设置材质节点 (218)
  registry.registerNodeType({
    type: 'render/setMaterial',
    category: NodeCategory.RENDER,
    constructor: SetMaterialNode,
    label: '设置材质',
    description: '设置物体的渲染材质',
    icon: 'material',
    color: '#4CAF50',
    tags: ['render', 'material', 'set']
  });

  // 注册获取材质节点 (219)
  registry.registerNodeType({
    type: 'render/getMaterial',
    category: NodeCategory.RENDER,
    constructor: GetMaterialNode,
    label: '获取材质',
    description: '获取物体的当前材质',
    icon: 'material',
    color: '#4CAF50',
    tags: ['render', 'material', 'get']
  });

  // 注册设置材质颜色节点 (220)
  registry.registerNodeType({
    type: 'render/setMaterialColor',
    category: NodeCategory.RENDER,
    constructor: SetMaterialColorNode,
    label: '设置材质颜色',
    description: '设置材质的颜色属性',
    icon: 'color',
    color: '#4CAF50',
    tags: ['render', 'material', 'color', 'set']
  });

  // 注册获取材质颜色节点 (221)
  registry.registerNodeType({
    type: 'render/getMaterialColor',
    category: NodeCategory.RENDER,
    constructor: GetMaterialColorNode,
    label: '获取材质颜色',
    description: '获取材质的颜色属性',
    icon: 'color',
    color: '#4CAF50',
    tags: ['render', 'material', 'color', 'get']
  });

  // 注册设置材质纹理节点 (222)
  registry.registerNodeType({
    type: 'render/setMaterialTexture',
    category: NodeCategory.RENDER,
    constructor: SetMaterialTextureNode,
    label: '设置材质纹理',
    description: '设置材质的纹理',
    icon: 'texture',
    color: '#4CAF50',
    tags: ['render', 'material', 'texture', 'set']
  });

  // 注册设置物体可见性节点 (223)
  registry.registerNodeType({
    type: 'render/setVisibility',
    category: NodeCategory.RENDER,
    constructor: SetVisibilityNode,
    label: '设置物体可见性',
    description: '设置物体是否可见',
    icon: 'visibility',
    color: '#4CAF50',
    tags: ['render', 'visibility', 'set']
  });

  // 注册设置相机位置节点 (225)
  registry.registerNodeType({
    type: 'camera/setPosition',
    category: NodeCategory.CAMERA,
    constructor: SetCameraPositionNode,
    label: '设置相机位置',
    description: '设置相机的位置',
    icon: 'camera',
    color: '#2196F3',
    tags: ['camera', 'position', 'set']
  });

  // 注册获取相机位置节点 (226)
  registry.registerNodeType({
    type: 'camera/getPosition',
    category: NodeCategory.CAMERA,
    constructor: GetCameraPositionNode,
    label: '获取相机位置',
    description: '获取相机的当前位置',
    icon: 'camera',
    color: '#2196F3',
    tags: ['camera', 'position', 'get']
  });

  // 注册相机朝向节点 (227)
  registry.registerNodeType({
    type: 'camera/lookAt',
    category: NodeCategory.CAMERA,
    constructor: CameraLookAtNode,
    label: '相机朝向',
    description: '设置相机朝向目标',
    icon: 'camera',
    color: '#2196F3',
    tags: ['camera', 'lookat', 'direction']
  });

  // 注册设置视野角度节点 (228)
  registry.registerNodeType({
    type: 'camera/setFOV',
    category: NodeCategory.CAMERA,
    constructor: SetCameraFOVNode,
    label: '设置视野角度',
    description: '设置相机的视野角度',
    icon: 'camera',
    color: '#2196F3',
    tags: ['camera', 'fov', 'angle']
  });

  // 注册获取视野角度节点 (229)
  registry.registerNodeType({
    type: 'camera/getFOV',
    category: NodeCategory.CAMERA,
    constructor: GetCameraFOVNode,
    label: '获取视野角度',
    description: '获取相机的当前视野角度',
    icon: 'camera',
    color: '#2196F3',
    tags: ['camera', 'fov', 'get']
  });

  // 注册设置相机缩放节点 (230)
  registry.registerNodeType({
    type: 'camera/setZoom',
    category: NodeCategory.CAMERA,
    constructor: SetCameraZoomNode,
    label: '设置相机缩放',
    description: '设置相机的缩放级别',
    icon: 'camera',
    color: '#2196F3',
    tags: ['camera', 'zoom', 'scale']
  });
}
