/**
 * 视觉脚本调试器
 * 提供节点执行状态可视化和调试功能
 */
import { NodeErrorHandler, ErrorLevel } from '../utils/NodeErrorHandler';
import { NodePerformanceMonitor } from '../utils/NodePerformanceMonitor';

export interface NodeExecutionState {
  nodeId: string;
  nodeType: string;
  status: 'idle' | 'executing' | 'completed' | 'error';
  executionTime?: number;
  inputValues?: Record<string, any>;
  outputValues?: Record<string, any>;
  errorMessage?: string;
  timestamp: number;
}

export interface DebugSession {
  id: string;
  name: string;
  startTime: number;
  endTime?: number;
  nodeStates: Map<string, NodeExecutionState>;
  totalExecutions: number;
  totalErrors: number;
}

/**
 * 可视化调试器
 */
export class VisualDebugger {
  private static instance: VisualDebugger;
  private isEnabled: boolean = false;
  private currentSession: DebugSession | null = null;
  private sessions: DebugSession[] = [];
  private debugPanel: HTMLElement | null = null;
  private nodeStateElements: Map<string, HTMLElement> = new Map();

  private constructor() {
    this.setupErrorListener();
  }

  public static getInstance(): VisualDebugger {
    if (!VisualDebugger.instance) {
      VisualDebugger.instance = new VisualDebugger();
    }
    return VisualDebugger.instance;
  }

  /**
   * 启用或禁用调试器
   * @param enabled 是否启用
   */
  public setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
    if (enabled) {
      this.createDebugPanel();
    } else {
      this.destroyDebugPanel();
    }
  }

  /**
   * 开始新的调试会话
   * @param name 会话名称
   */
  public startSession(name: string): string {
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    this.currentSession = {
      id: sessionId,
      name,
      startTime: Date.now(),
      nodeStates: new Map(),
      totalExecutions: 0,
      totalErrors: 0
    };

    this.sessions.push(this.currentSession);
    this.updateDebugPanel();
    
    return sessionId;
  }

  /**
   * 结束当前调试会话
   */
  public endSession(): void {
    if (this.currentSession) {
      this.currentSession.endTime = Date.now();
      this.currentSession = null;
      this.updateDebugPanel();
    }
  }

  /**
   * 记录节点执行状态
   * @param nodeId 节点ID
   * @param nodeType 节点类型
   * @param status 执行状态
   * @param data 额外数据
   */
  public recordNodeState(
    nodeId: string,
    nodeType: string,
    status: NodeExecutionState['status'],
    data?: {
      executionTime?: number;
      inputValues?: Record<string, any>;
      outputValues?: Record<string, any>;
      errorMessage?: string;
    }
  ): void {
    if (!this.isEnabled || !this.currentSession) return;

    const state: NodeExecutionState = {
      nodeId,
      nodeType,
      status,
      timestamp: Date.now(),
      ...data
    };

    this.currentSession.nodeStates.set(nodeId, state);
    this.currentSession.totalExecutions++;
    
    if (status === 'error') {
      this.currentSession.totalErrors++;
    }

    this.updateNodeVisualization(nodeId, state);
    this.updateDebugPanel();
  }

  /**
   * 创建调试面板
   */
  private createDebugPanel(): void {
    if (this.debugPanel) return;

    this.debugPanel = document.createElement('div');
    this.debugPanel.id = 'visual-script-debugger';
    this.debugPanel.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      width: 400px;
      max-height: 600px;
      background: rgba(0, 0, 0, 0.9);
      color: white;
      border-radius: 8px;
      padding: 16px;
      font-family: 'Courier New', monospace;
      font-size: 12px;
      z-index: 10000;
      overflow-y: auto;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    `;

    document.body.appendChild(this.debugPanel);
    this.updateDebugPanel();
  }

  /**
   * 销毁调试面板
   */
  private destroyDebugPanel(): void {
    if (this.debugPanel) {
      document.body.removeChild(this.debugPanel);
      this.debugPanel = null;
    }
    this.nodeStateElements.clear();
  }

  /**
   * 更新调试面板内容
   */
  private updateDebugPanel(): void {
    if (!this.debugPanel) return;

    const performanceMonitor = NodePerformanceMonitor.getInstance();
    const errorHandler = NodeErrorHandler.getInstance();

    let html = `
      <div style="border-bottom: 1px solid #333; padding-bottom: 8px; margin-bottom: 8px;">
        <h3 style="margin: 0; color: #4CAF50;">🔍 视觉脚本调试器</h3>
      </div>
    `;

    // 当前会话信息
    if (this.currentSession) {
      const duration = Date.now() - this.currentSession.startTime;
      html += `
        <div style="margin-bottom: 12px;">
          <div style="color: #2196F3;">📊 当前会话: ${this.currentSession.name}</div>
          <div>⏱️ 运行时间: ${(duration / 1000).toFixed(1)}s</div>
          <div>🔄 执行次数: ${this.currentSession.totalExecutions}</div>
          <div style="color: ${this.currentSession.totalErrors > 0 ? '#f44336' : '#4CAF50'};">
            ❌ 错误数量: ${this.currentSession.totalErrors}
          </div>
        </div>
      `;

      // 节点状态列表
      if (this.currentSession.nodeStates.size > 0) {
        html += `<div style="border-top: 1px solid #333; padding-top: 8px;">`;
        html += `<div style="color: #FF9800; margin-bottom: 8px;">🎯 节点状态</div>`;
        
        this.currentSession.nodeStates.forEach((state, nodeId) => {
          const statusColor = this.getStatusColor(state.status);
          const statusIcon = this.getStatusIcon(state.status);
          
          html += `
            <div style="margin-bottom: 4px; padding: 4px; background: rgba(255,255,255,0.1); border-radius: 4px;">
              <div style="color: ${statusColor};">${statusIcon} ${state.nodeType}</div>
              <div style="font-size: 10px; color: #ccc;">ID: ${nodeId}</div>
              ${state.executionTime ? `<div style="font-size: 10px;">⏱️ ${state.executionTime.toFixed(2)}ms</div>` : ''}
              ${state.errorMessage ? `<div style="font-size: 10px; color: #f44336;">💥 ${state.errorMessage}</div>` : ''}
            </div>
          `;
        });
        
        html += `</div>`;
      }
    } else {
      html += `<div style="color: #666;">没有活动的调试会话</div>`;
    }

    // 性能统计
    const recentMetrics = performanceMonitor.getRecentMetrics(30000); // 最近30秒
    if (recentMetrics.length > 0) {
      const avgTime = recentMetrics.reduce((sum, m) => sum + m.executionTime, 0) / recentMetrics.length;
      html += `
        <div style="border-top: 1px solid #333; padding-top: 8px; margin-top: 8px;">
          <div style="color: #9C27B0; margin-bottom: 8px;">⚡ 性能统计 (30s)</div>
          <div>📈 平均执行时间: ${avgTime.toFixed(2)}ms</div>
          <div>🔢 执行次数: ${recentMetrics.length}</div>
        </div>
      `;
    }

    // 最近错误
    const recentErrors = errorHandler.getErrorsByLevel(ErrorLevel.ERROR).slice(-3);
    if (recentErrors.length > 0) {
      html += `
        <div style="border-top: 1px solid #333; padding-top: 8px; margin-top: 8px;">
          <div style="color: #f44336; margin-bottom: 8px;">🚨 最近错误</div>
      `;
      
      recentErrors.forEach(error => {
        html += `
          <div style="margin-bottom: 4px; padding: 4px; background: rgba(244,67,54,0.2); border-radius: 4px;">
            <div style="font-size: 10px;">${error.nodeType}:${error.nodeId}</div>
            <div style="font-size: 10px; color: #ffcdd2;">${error.message}</div>
          </div>
        `;
      });
      
      html += `</div>`;
    }

    // 控制按钮
    html += `
      <div style="border-top: 1px solid #333; padding-top: 8px; margin-top: 8px;">
        <button onclick="window.visualDebugger.generateReport()" style="
          background: #4CAF50; color: white; border: none; padding: 4px 8px; 
          border-radius: 4px; font-size: 10px; margin-right: 4px; cursor: pointer;
        ">📊 生成报告</button>
        <button onclick="window.visualDebugger.clearData()" style="
          background: #f44336; color: white; border: none; padding: 4px 8px; 
          border-radius: 4px; font-size: 10px; cursor: pointer;
        ">🗑️ 清除数据</button>
      </div>
    `;

    this.debugPanel.innerHTML = html;

    // 暴露到全局以便按钮调用
    (window as any).visualDebugger = this;
  }

  /**
   * 更新节点可视化
   * @param nodeId 节点ID
   * @param state 节点状态
   */
  private updateNodeVisualization(nodeId: string, state: NodeExecutionState): void {
    // 这里可以添加在画布上高亮显示节点状态的逻辑
    // 例如改变节点边框颜色、添加状态指示器等
    console.log(`节点 ${nodeId} 状态更新:`, state.status);
  }

  /**
   * 获取状态颜色
   * @param status 状态
   * @returns 颜色值
   */
  private getStatusColor(status: NodeExecutionState['status']): string {
    switch (status) {
      case 'idle': return '#666';
      case 'executing': return '#FF9800';
      case 'completed': return '#4CAF50';
      case 'error': return '#f44336';
      default: return '#666';
    }
  }

  /**
   * 获取状态图标
   * @param status 状态
   * @returns 图标
   */
  private getStatusIcon(status: NodeExecutionState['status']): string {
    switch (status) {
      case 'idle': return '⏸️';
      case 'executing': return '⚡';
      case 'completed': return '✅';
      case 'error': return '❌';
      default: return '❓';
    }
  }

  /**
   * 设置错误监听器
   */
  private setupErrorListener(): void {
    const errorHandler = NodeErrorHandler.getInstance();
    errorHandler.addErrorListener((error) => {
      if (this.isEnabled && this.currentSession) {
        this.recordNodeState(
          error.nodeId,
          error.nodeType,
          'error',
          { errorMessage: error.message }
        );
      }
    });
  }

  /**
   * 生成调试报告
   */
  public generateReport(): void {
    const performanceMonitor = NodePerformanceMonitor.getInstance();
    const report = performanceMonitor.generateReport();
    
    // 创建报告窗口
    const reportWindow = window.open('', '_blank', 'width=800,height=600');
    if (reportWindow) {
      reportWindow.document.write(`
        <html>
          <head><title>视觉脚本调试报告</title></head>
          <body style="font-family: monospace; padding: 20px; background: #f5f5f5;">
            <h1>🔍 视觉脚本调试报告</h1>
            <pre style="background: white; padding: 20px; border-radius: 8px; overflow: auto;">
${report}
            </pre>
          </body>
        </html>
      `);
      reportWindow.document.close();
    }
  }

  /**
   * 清除调试数据
   */
  public clearData(): void {
    const performanceMonitor = NodePerformanceMonitor.getInstance();
    const errorHandler = NodeErrorHandler.getInstance();
    
    performanceMonitor.clearMetrics();
    errorHandler.clearErrors();
    this.sessions = [];
    this.currentSession = null;
    this.nodeStateElements.clear();
    
    this.updateDebugPanel();
    console.log('🗑️ 调试数据已清除');
  }

  /**
   * 获取调试会话列表
   * @returns 会话列表
   */
  public getSessions(): DebugSession[] {
    return [...this.sessions];
  }

  /**
   * 获取当前会话
   * @returns 当前会话
   */
  public getCurrentSession(): DebugSession | null {
    return this.currentSession;
  }
}
