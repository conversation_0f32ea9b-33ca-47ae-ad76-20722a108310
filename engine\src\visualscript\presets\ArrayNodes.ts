/**
 * 视觉脚本数组节点
 * 提供数组操作相关的节点
 */
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeCategory, NodeType, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry, NodeTypeInfo } from '../nodes/NodeRegistry';

/**
 * 数组创建节点
 * 创建一个新的数组
 */
export class ArrayCreateNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加初始大小输入
    this.addInput({
      name: 'size',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '初始大小',
      defaultValue: 0,
      optional: true
    });

    // 添加初始值输入
    this.addInput({
      name: 'initialValue',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'any',
      description: '初始值',
      defaultValue: null,
      optional: true
    });

    // 添加数组输出
    this.addOutput({
      name: 'array',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'array',
      description: '创建的数组'
    });

    // 添加长度输出
    this.addOutput({
      name: 'length',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '数组长度'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const size = Number(this.getInputValue('size') || 0);
    const initialValue = this.getInputValue('initialValue');

    // 创建数组
    let result: any[];
    if (size > 0) {
      result = new Array(size).fill(initialValue);
    } else {
      result = [];
    }

    // 设置输出值
    this.setOutputValue('array', result);
    this.setOutputValue('length', result.length);

    return result;
  }
}

/**
 * 数组添加元素节点
 * 向数组末尾添加元素
 */
export class ArrayPushNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加数组输入
    this.addInput({
      name: 'array',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'array',
      description: '目标数组',
      defaultValue: []
    });

    // 添加元素输入
    this.addInput({
      name: 'element',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'any',
      description: '要添加的元素',
      defaultValue: null
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'array',
      description: '修改后的数组'
    });

    // 添加长度输出
    this.addOutput({
      name: 'length',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '数组长度'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const inputArray = this.getInputValue('array') || [];
    const element = this.getInputValue('element');

    // 创建新数组副本并添加元素
    const result = Array.isArray(inputArray) ? [...inputArray] : [];
    result.push(element);

    // 设置输出值
    this.setOutputValue('result', result);
    this.setOutputValue('length', result.length);

    return result;
  }
}

/**
 * 数组移除元素节点
 * 移除并返回数组末尾元素
 */
export class ArrayPopNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加数组输入
    this.addInput({
      name: 'array',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'array',
      description: '目标数组',
      defaultValue: []
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'array',
      description: '修改后的数组'
    });

    // 添加移除的元素输出
    this.addOutput({
      name: 'element',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'any',
      description: '移除的元素'
    });

    // 添加长度输出
    this.addOutput({
      name: 'length',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '数组长度'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const inputArray = this.getInputValue('array') || [];

    // 创建新数组副本并移除元素
    const result = Array.isArray(inputArray) ? [...inputArray] : [];
    const element = result.pop();

    // 设置输出值
    this.setOutputValue('result', result);
    this.setOutputValue('element', element);
    this.setOutputValue('length', result.length);

    return result;
  }
}

/**
 * 数组移除首元素节点
 * 移除并返回数组首个元素
 */
export class ArrayShiftNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加数组输入
    this.addInput({
      name: 'array',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'array',
      description: '目标数组',
      defaultValue: []
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'array',
      description: '修改后的数组'
    });

    // 添加移除的元素输出
    this.addOutput({
      name: 'element',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'any',
      description: '移除的首个元素'
    });

    // 添加长度输出
    this.addOutput({
      name: 'length',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '数组长度'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const inputArray = this.getInputValue('array') || [];

    // 创建新数组副本并移除首个元素
    const result = Array.isArray(inputArray) ? [...inputArray] : [];
    const element = result.shift();

    // 设置输出值
    this.setOutputValue('result', result);
    this.setOutputValue('element', element);
    this.setOutputValue('length', result.length);

    return result;
  }
}

/**
 * 数组添加首元素节点
 * 向数组开头添加元素
 */
export class ArrayUnshiftNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加数组输入
    this.addInput({
      name: 'array',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'array',
      description: '目标数组',
      defaultValue: []
    });

    // 添加元素输入
    this.addInput({
      name: 'element',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'any',
      description: '要添加的元素',
      defaultValue: null
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'array',
      description: '修改后的数组'
    });

    // 添加长度输出
    this.addOutput({
      name: 'length',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '数组长度'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const inputArray = this.getInputValue('array') || [];
    const element = this.getInputValue('element');

    // 创建新数组副本并在开头添加元素
    const result = Array.isArray(inputArray) ? [...inputArray] : [];
    result.unshift(element);

    // 设置输出值
    this.setOutputValue('result', result);
    this.setOutputValue('length', result.length);

    return result;
  }
}

/**
 * 数组长度节点
 * 获取数组的长度
 */
export class ArrayLengthNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加数组输入
    this.addInput({
      name: 'array',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'array',
      description: '目标数组',
      defaultValue: []
    });

    // 添加长度输出
    this.addOutput({
      name: 'length',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '数组长度'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const inputArray = this.getInputValue('array') || [];

    // 获取数组长度
    const length = Array.isArray(inputArray) ? inputArray.length : 0;

    // 设置输出值
    this.setOutputValue('length', length);

    return length;
  }
}

/**
 * 数组获取元素节点
 * 获取指定索引的数组元素
 */
export class ArrayGetNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加数组输入
    this.addInput({
      name: 'array',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'array',
      description: '目标数组',
      defaultValue: []
    });

    // 添加索引输入
    this.addInput({
      name: 'index',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '元素索引',
      defaultValue: 0
    });

    // 添加默认值输入
    this.addInput({
      name: 'defaultValue',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'any',
      description: '默认值（索引超出范围时返回）',
      defaultValue: null,
      optional: true
    });

    // 添加元素输出
    this.addOutput({
      name: 'element',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'any',
      description: '获取的元素'
    });

    // 添加有效性输出
    this.addOutput({
      name: 'valid',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '索引是否有效'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const inputArray = this.getInputValue('array') || [];
    const index = Number(this.getInputValue('index') || 0);
    const defaultValue = this.getInputValue('defaultValue');

    // 检查数组和索引有效性
    const isValid = Array.isArray(inputArray) && index >= 0 && index < inputArray.length;
    const element = isValid ? inputArray[index] : defaultValue;

    // 设置输出值
    this.setOutputValue('element', element);
    this.setOutputValue('valid', isValid);

    return element;
  }
}

/**
 * 数组设置元素节点
 * 设置指定索引的数组元素
 */
export class ArraySetNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加数组输入
    this.addInput({
      name: 'array',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'array',
      description: '目标数组',
      defaultValue: []
    });

    // 添加索引输入
    this.addInput({
      name: 'index',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '元素索引',
      defaultValue: 0
    });

    // 添加值输入
    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'any',
      description: '要设置的值',
      defaultValue: null
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'array',
      description: '修改后的数组'
    });

    // 添加成功输出
    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '设置是否成功'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const inputArray = this.getInputValue('array') || [];
    const index = Number(this.getInputValue('index') || 0);
    const value = this.getInputValue('value');

    // 创建数组副本
    const result = Array.isArray(inputArray) ? [...inputArray] : [];
    let success = false;

    // 检查索引有效性并设置值
    if (index >= 0 && index < result.length) {
      result[index] = value;
      success = true;
    } else if (index >= 0) {
      // 如果索引超出范围但为正数，扩展数组
      while (result.length <= index) {
        result.push(undefined);
      }
      result[index] = value;
      success = true;
    }

    // 设置输出值
    this.setOutputValue('result', result);
    this.setOutputValue('success', success);

    return result;
  }
}

/**
 * 数组查找索引节点
 * 查找元素在数组中的索引
 */
export class ArrayIndexOfNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加数组输入
    this.addInput({
      name: 'array',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'array',
      description: '目标数组',
      defaultValue: []
    });

    // 添加搜索元素输入
    this.addInput({
      name: 'element',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'any',
      description: '要查找的元素',
      defaultValue: null
    });

    // 添加开始位置输入
    this.addInput({
      name: 'startIndex',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '开始搜索位置',
      defaultValue: 0,
      optional: true
    });

    // 添加索引输出
    this.addOutput({
      name: 'index',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '找到的索引（-1表示未找到）'
    });

    // 添加是否找到输出
    this.addOutput({
      name: 'found',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否找到'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const inputArray = this.getInputValue('array') || [];
    const element = this.getInputValue('element');
    const startIndex = Number(this.getInputValue('startIndex') || 0);

    // 查找元素索引
    let index = -1;
    if (Array.isArray(inputArray)) {
      index = inputArray.indexOf(element, startIndex);
    }

    const found = index !== -1;

    // 设置输出值
    this.setOutputValue('index', index);
    this.setOutputValue('found', found);

    return { index, found };
  }
}

/**
 * 数组包含检查节点
 * 检查数组是否包含指定元素
 */
export class ArrayContainsNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加数组输入
    this.addInput({
      name: 'array',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'array',
      description: '目标数组',
      defaultValue: []
    });

    // 添加搜索元素输入
    this.addInput({
      name: 'element',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'any',
      description: '要查找的元素',
      defaultValue: null
    });

    // 添加是否包含输出
    this.addOutput({
      name: 'contains',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否包含'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const inputArray = this.getInputValue('array') || [];
    const element = this.getInputValue('element');

    // 检查是否包含
    const contains = Array.isArray(inputArray) ? inputArray.includes(element) : false;

    // 设置输出值
    this.setOutputValue('contains', contains);

    return contains;
  }
}

/**
 * 数组切片节点
 * 提取数组的一部分
 */
export class ArraySliceNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加数组输入
    this.addInput({
      name: 'array',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'array',
      description: '目标数组',
      defaultValue: []
    });

    // 添加开始位置输入
    this.addInput({
      name: 'start',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '开始位置',
      defaultValue: 0
    });

    // 添加结束位置输入
    this.addInput({
      name: 'end',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '结束位置（可选）',
      defaultValue: -1,
      optional: true
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'array',
      description: '切片后的数组'
    });

    // 添加长度输出
    this.addOutput({
      name: 'length',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '切片长度'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const inputArray = this.getInputValue('array') || [];
    const start = Number(this.getInputValue('start') || 0);
    const end = Number(this.getInputValue('end') || -1);

    // 执行切片操作
    let result: any[] = [];
    if (Array.isArray(inputArray)) {
      if (end === -1) {
        result = inputArray.slice(start);
      } else {
        result = inputArray.slice(start, end);
      }
    }

    // 设置输出值
    this.setOutputValue('result', result);
    this.setOutputValue('length', result.length);

    return result;
  }
}

/**
 * 数组连接节点
 * 将数组元素连接成字符串
 */
export class ArrayJoinNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加数组输入
    this.addInput({
      name: 'array',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'array',
      description: '目标数组',
      defaultValue: []
    });

    // 添加分隔符输入
    this.addInput({
      name: 'separator',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '分隔符',
      defaultValue: ','
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '连接后的字符串'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const inputArray = this.getInputValue('array') || [];
    const separator = String(this.getInputValue('separator') || ',');

    // 连接数组元素
    let result = '';
    if (Array.isArray(inputArray)) {
      result = inputArray.join(separator);
    }

    // 设置输出值
    this.setOutputValue('result', result);

    return result;
  }
}

/**
 * 数组反转节点
 * 反转数组元素顺序
 */
export class ArrayReverseNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加数组输入
    this.addInput({
      name: 'array',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'array',
      description: '目标数组',
      defaultValue: []
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'array',
      description: '反转后的数组'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const inputArray = this.getInputValue('array') || [];

    // 创建数组副本并反转
    let result: any[] = [];
    if (Array.isArray(inputArray)) {
      result = [...inputArray].reverse();
    }

    // 设置输出值
    this.setOutputValue('result', result);

    return result;
  }
}

/**
 * 数组排序节点
 * 对数组元素进行排序
 */
export class ArraySortNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加数组输入
    this.addInput({
      name: 'array',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'array',
      description: '目标数组',
      defaultValue: []
    });

    // 添加排序方向输入
    this.addInput({
      name: 'ascending',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'boolean',
      description: '是否升序排列',
      defaultValue: true,
      optional: true
    });

    // 添加数字排序输入
    this.addInput({
      name: 'numeric',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'boolean',
      description: '是否按数字排序',
      defaultValue: false,
      optional: true
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'array',
      description: '排序后的数组'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const inputArray = this.getInputValue('array') || [];
    const ascending = Boolean(this.getInputValue('ascending') !== false);
    const numeric = Boolean(this.getInputValue('numeric') || false);

    // 创建数组副本并排序
    let result: any[] = [];
    if (Array.isArray(inputArray)) {
      result = [...inputArray];

      if (numeric) {
        // 数字排序
        result.sort((a, b) => {
          const numA = Number(a);
          const numB = Number(b);
          return ascending ? numA - numB : numB - numA;
        });
      } else {
        // 字符串排序
        result.sort((a, b) => {
          const strA = String(a);
          const strB = String(b);
          if (ascending) {
            return strA.localeCompare(strB);
          } else {
            return strB.localeCompare(strA);
          }
        });
      }
    }

    // 设置输出值
    this.setOutputValue('result', result);

    return result;
  }
}

/**
 * 注册数组节点
 * @param registry 节点注册表
 */
export function registerArrayNodes(registry: NodeRegistry): void {
  // 注册数组创建节点
  registry.registerNodeType({
    type: 'array/create',
    category: NodeCategory.ARRAY,
    constructor: ArrayCreateNode,
    label: '创建数组',
    description: '创建一个新的数组',
    icon: 'plus-square',
    color: '#1890FF',
    tags: ['array', 'create', 'new']
  });

  // 注册数组添加元素节点
  registry.registerNodeType({
    type: 'array/push',
    category: NodeCategory.ARRAY,
    constructor: ArrayPushNode,
    label: '添加元素',
    description: '向数组末尾添加元素',
    icon: 'plus-circle',
    color: '#1890FF',
    tags: ['array', 'push', 'add']
  });

  // 注册数组移除元素节点
  registry.registerNodeType({
    type: 'array/pop',
    category: NodeCategory.ARRAY,
    constructor: ArrayPopNode,
    label: '移除末尾元素',
    description: '移除并返回数组末尾元素',
    icon: 'minus-circle',
    color: '#1890FF',
    tags: ['array', 'pop', 'remove']
  });

  // 注册数组移除首元素节点
  registry.registerNodeType({
    type: 'array/shift',
    category: NodeCategory.ARRAY,
    constructor: ArrayShiftNode,
    label: '移除首元素',
    description: '移除并返回数组首个元素',
    icon: 'arrow-left',
    color: '#1890FF',
    tags: ['array', 'shift', 'remove', 'first']
  });

  // 注册数组添加首元素节点
  registry.registerNodeType({
    type: 'array/unshift',
    category: NodeCategory.ARRAY,
    constructor: ArrayUnshiftNode,
    label: '添加首元素',
    description: '向数组开头添加元素',
    icon: 'arrow-right',
    color: '#1890FF',
    tags: ['array', 'unshift', 'add', 'first']
  });

  // 注册数组长度节点
  registry.registerNodeType({
    type: 'array/length',
    category: NodeCategory.ARRAY,
    constructor: ArrayLengthNode,
    label: '数组长度',
    description: '获取数组的长度',
    icon: 'number',
    color: '#1890FF',
    tags: ['array', 'length', 'size']
  });

  // 注册数组获取元素节点
  registry.registerNodeType({
    type: 'array/get',
    category: NodeCategory.ARRAY,
    constructor: ArrayGetNode,
    label: '获取元素',
    description: '获取指定索引的数组元素',
    icon: 'eye',
    color: '#1890FF',
    tags: ['array', 'get', 'index', 'element']
  });

  // 注册数组设置元素节点
  registry.registerNodeType({
    type: 'array/set',
    category: NodeCategory.ARRAY,
    constructor: ArraySetNode,
    label: '设置元素',
    description: '设置指定索引的数组元素',
    icon: 'edit',
    color: '#1890FF',
    tags: ['array', 'set', 'index', 'element']
  });

  // 注册数组查找索引节点
  registry.registerNodeType({
    type: 'array/indexOf',
    category: NodeCategory.ARRAY,
    constructor: ArrayIndexOfNode,
    label: '查找索引',
    description: '查找元素在数组中的索引',
    icon: 'search',
    color: '#1890FF',
    tags: ['array', 'indexOf', 'search', 'find']
  });

  // 注册数组包含检查节点
  registry.registerNodeType({
    type: 'array/contains',
    category: NodeCategory.ARRAY,
    constructor: ArrayContainsNode,
    label: '包含检查',
    description: '检查数组是否包含指定元素',
    icon: 'check',
    color: '#1890FF',
    tags: ['array', 'contains', 'includes', 'check']
  });

  // 注册数组切片节点
  registry.registerNodeType({
    type: 'array/slice',
    category: NodeCategory.ARRAY,
    constructor: ArraySliceNode,
    label: '数组切片',
    description: '提取数组的一部分',
    icon: 'scissor',
    color: '#1890FF',
    tags: ['array', 'slice', 'extract', 'part']
  });

  // 注册数组连接节点
  registry.registerNodeType({
    type: 'array/join',
    category: NodeCategory.ARRAY,
    constructor: ArrayJoinNode,
    label: '数组连接',
    description: '将数组元素连接成字符串',
    icon: 'link',
    color: '#1890FF',
    tags: ['array', 'join', 'string', 'concat']
  });

  // 注册数组反转节点
  registry.registerNodeType({
    type: 'array/reverse',
    category: NodeCategory.ARRAY,
    constructor: ArrayReverseNode,
    label: '数组反转',
    description: '反转数组元素顺序',
    icon: 'swap',
    color: '#1890FF',
    tags: ['array', 'reverse', 'flip']
  });

  // 注册数组排序节点
  registry.registerNodeType({
    type: 'array/sort',
    category: NodeCategory.ARRAY,
    constructor: ArraySortNode,
    label: '数组排序',
    description: '对数组元素进行排序',
    icon: 'sort',
    color: '#1890FF',
    tags: ['array', 'sort', 'order']
  });
}
