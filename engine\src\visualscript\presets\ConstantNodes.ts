/**
 * 常量节点
 * 提供各种常量值的节点实现
 */
import { FunctionNode } from '../nodes/FunctionNode';
import { SocketType, SocketDirection, NodeCategory } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';

/**
 * 数字常量节点
 * 输出指定的数字常量
 */
export class NumberConstantNode extends FunctionNode {
  /** 常量值 */
  private constantValue: number;

  /**
   * 创建数字常量节点
   * @param options 节点选项
   */
  constructor(options: any) {
    super(options);

    // 设置常量值
    this.constantValue = options.metadata?.value || 0;
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加数值输入（可编辑）
    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '常量值',
      defaultValue: this.constantValue,
      isEditable: true
    });

    // 添加数值输出
    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '数字常量'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值（如果有连接则使用连接的值，否则使用默认值）
    const value = this.getInputValue('value') as number;

    // 设置输出值
    this.setOutputValue('value', value);

    return value;
  }

  /**
   * 获取节点显示名称
   */
  public getDisplayName(): string {
    const value = this.getInputValue('value') as number;
    return `数字: ${value}`;
  }
}

/**
 * 字符串常量节点
 * 输出指定的字符串常量
 */
export class StringConstantNode extends FunctionNode {
  /** 常量值 */
  private constantValue: string;

  /**
   * 创建字符串常量节点
   * @param options 节点选项
   */
  constructor(options: any) {
    super(options);

    // 设置常量值
    this.constantValue = options.metadata?.value || '';
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加字符串输入（可编辑）
    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '常量值',
      defaultValue: this.constantValue,
      isEditable: true
    });

    // 添加字符串输出
    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '字符串常量'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值（如果有连接则使用连接值，否则使用默认值）
    const inputValue = this.getInputValue('value');
    const value = inputValue !== undefined ? String(inputValue) : this.constantValue;

    // 设置输出值
    this.setOutputValue('value', value);

    return value;
  }

  /**
   * 获取节点显示名称
   */
  public getDisplayName(): string {
    const value = this.getInputValue('value') as string;
    return `字符串: "${value}"`;
  }
}

/**
 * 布尔常量节点
 * 输出指定的布尔常量
 */
export class BooleanConstantNode extends FunctionNode {
  /** 常量值 */
  private constantValue: boolean;

  /**
   * 创建布尔常量节点
   * @param options 节点选项
   */
  constructor(options: any) {
    super(options);

    // 设置常量值
    this.constantValue = options.metadata?.value || false;
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加布尔输入（可编辑）
    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'boolean',
      description: '常量值',
      defaultValue: this.constantValue,
      isEditable: true
    });

    // 添加布尔输出
    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '布尔常量'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值（如果有连接则使用连接值，否则使用默认值）
    const inputValue = this.getInputValue('value');
    const value = inputValue !== undefined ? Boolean(inputValue) : this.constantValue;

    // 设置输出值
    this.setOutputValue('value', value);

    return value;
  }

  /**
   * 获取节点显示名称
   */
  public getDisplayName(): string {
    const value = this.getInputValue('value') as boolean;
    return `布尔: ${value ? '真' : '假'}`;
  }
}

/**
 * 注册常量节点
 * @param registry 节点注册表
 */
export function registerConstantNodes(registry: NodeRegistry): void {
  // 注册数字常量节点
  registry.registerNodeType({
    type: 'constant/number',
    category: NodeCategory.CONSTANT,
    constructor: NumberConstantNode,
    label: '数字常量',
    description: '输出指定的数字常量',
    icon: 'number',
    color: '#52C41A',
    tags: ['constant', 'number', 'value']
  });

  // 注册字符串常量节点
  registry.registerNodeType({
    type: 'constant/string',
    category: NodeCategory.CONSTANT,
    constructor: StringConstantNode,
    label: '字符串常量',
    description: '输出指定的字符串常量',
    icon: 'font-size',
    color: '#52C41A',
    tags: ['constant', 'string', 'text']
  });

  // 注册布尔常量节点
  registry.registerNodeType({
    type: 'constant/boolean',
    category: NodeCategory.CONSTANT,
    constructor: BooleanConstantNode,
    label: '布尔常量',
    description: '输出指定的布尔常量',
    icon: 'check-circle',
    color: '#52C41A',
    tags: ['constant', 'boolean', 'true', 'false']
  });
}
