# 新节点使用示例 (136-151)

本文档展示了如何在视觉脚本编辑器中使用新实现的16个节点。

## 时间操作节点 (136-141)

### 1. 获取当前时间 (136)
```
节点类型: time/getCurrentTime
功能: 获取当前系统时间戳
输出:
- timestamp: 当前时间戳（毫秒）
- seconds: 当前时间戳（秒）
```

### 2. 获取帧时间 (137)
```
节点类型: time/getDeltaTime
功能: 获取上一帧到当前帧的时间差
输出:
- deltaTime: 帧时间（秒）
- unscaledDeltaTime: 未缩放帧时间（秒）
```

### 3. 延时执行 (138)
```
节点类型: time/delay
功能: 延时指定时间后执行
输入:
- flow: 输入流程
- duration: 延时时间（秒）
输出:
- completed: 延时完成流程
- state: 节点状态
```

### 4. 计时器 (139)
```
节点类型: time/timer
功能: 创建一个计时器
输入:
- start: 开始计时流程
- stop: 停止计时流程
- reset: 重置计时器流程
- interval: 触发间隔（秒）
输出:
- tick: 计时器触发流程
- elapsed: 已用时间（秒）
- isRunning: 是否运行中
```

### 5. 秒表 (140)
```
节点类型: time/stopwatch
功能: 创建一个秒表计时器
输入:
- start: 开始秒表流程
- pause: 暂停秒表流程
- resume: 恢复秒表流程
- stop: 停止秒表流程
- reset: 重置秒表流程
输出:
- elapsed: 已用时间（秒）
- isRunning: 是否运行中
- isPaused: 是否暂停
```

### 6. 格式化时间 (141)
```
节点类型: time/formatTime
功能: 将时间戳格式化为可读字符串
输入:
- timestamp: 时间戳（毫秒）
- format: 格式字符串
- locale: 语言区域
输出:
- formatted: 格式化后的时间字符串
- date: 日期部分
- time: 时间部分
```

## 动画控制节点 (142-149)

### 7. 播放动画 (142)
```
节点类型: animation/play
功能: 播放指定的动画
输入:
- flow: 输入流程
- entity: 目标实体
- animationName: 动画名称
- loop: 是否循环播放
- blendTime: 混合时间（秒）
输出:
- completed: 播放完成流程
- started: 开始播放流程
- success: 是否成功播放
```

### 8. 停止动画 (143)
```
节点类型: animation/stop
功能: 停止当前播放的动画
输入:
- flow: 输入流程
- entity: 目标实体
- animationName: 动画名称（可选）
输出:
- completed: 停止完成流程
- success: 是否成功停止
```

### 9. 暂停动画 (144)
```
节点类型: animation/pause
功能: 暂停当前播放的动画
输入:
- flow: 输入流程
- entity: 目标实体
- animationName: 动画名称（可选）
输出:
- completed: 暂停完成流程
- success: 是否成功暂停
```

### 10. 恢复动画 (145)
```
节点类型: animation/resume
功能: 恢复暂停的动画播放
输入:
- flow: 输入流程
- entity: 目标实体
- animationName: 动画名称（可选）
输出:
- completed: 恢复完成流程
- success: 是否成功恢复
```

### 11. 设置动画速度 (146)
```
节点类型: animation/setSpeed
功能: 设置动画播放速度
输入:
- flow: 输入流程
- entity: 目标实体
- animationName: 动画名称（可选）
- speed: 播放速度
输出:
- completed: 设置完成流程
- success: 是否成功设置
```

### 12. 获取动画状态 (147)
```
节点类型: animation/getState
功能: 获取当前动画的播放状态
输入:
- entity: 目标实体
- animationName: 动画名称（可选）
输出:
- isPlaying: 是否播放中
- isPaused: 是否暂停
- currentClip: 当前动画名称
- time: 播放时间（秒）
- duration: 动画总时长（秒）
- progress: 播放进度（0-1）
```

### 13. 设置动画时间 (148)
```
节点类型: animation/setTime
功能: 设置动画播放到指定时间点
输入:
- flow: 输入流程
- entity: 目标实体
- animationName: 动画名称
- time: 目标时间（秒）
- progress: 目标进度（0-1）
输出:
- completed: 设置完成流程
- success: 是否成功设置
```

### 14. 动画混合 (149)
```
节点类型: animation/crossFade
功能: 在两个动画之间进行混合过渡
输入:
- flow: 输入流程
- entity: 目标实体
- fromAnimation: 源动画名称
- toAnimation: 目标动画名称
- blendTime: 混合时间（秒）
- weight: 混合权重（0-1）
输出:
- completed: 混合完成流程
- started: 开始混合流程
- success: 是否成功开始混合
```

## 输入处理节点 (150-151)

### 15. 按键按下 (150)
```
节点类型: input/keyboard/isKeyDown
功能: 检查指定按键是否被按下
输入:
- key: 按键代码
输出:
- isDown: 是否按下
- justPressed: 刚刚按下
```

### 16. 按键释放 (151)
```
节点类型: input/keyboard/isKeyUp
功能: 检查指定按键是否被释放
输入:
- key: 按键代码
输出:
- isUp: 是否释放
- justReleased: 刚刚释放
```

## 使用示例

### 简单的计时器示例
1. 拖拽"开始事件"节点到画布
2. 拖拽"计时器"节点到画布
3. 拖拽"日志"节点到画布
4. 连接：开始事件 → 计时器(start)
5. 连接：计时器(tick) → 日志节点
6. 设置计时器间隔为1秒
7. 运行脚本，每秒输出一次日志

### 动画播放示例
1. 拖拽"开始事件"节点到画布
2. 拖拽"获取实体"节点到画布
3. 拖拽"播放动画"节点到画布
4. 连接：开始事件 → 获取实体 → 播放动画
5. 设置实体ID和动画名称
6. 运行脚本，播放指定动画

### 按键检测示例
1. 拖拽"更新事件"节点到画布
2. 拖拽"按键按下"节点到画布
3. 拖拽"分支"节点到画布
4. 拖拽"日志"节点到画布
5. 连接：更新事件 → 按键按下 → 分支(condition)
6. 连接：分支(true) → 日志节点
7. 设置按键为"Space"
8. 运行脚本，按空格键时输出日志

## 注意事项

1. 所有新节点都已在引擎中注册，可以在编辑器的节点面板中找到
2. 时间节点位于"时间操作"分类下
3. 动画节点位于"动画控制"分类下
4. 输入节点位于"输入处理"分类下
5. 节点的中文名称便于理解和使用
6. 所有节点都支持拖拽连接，实现可视化编程
