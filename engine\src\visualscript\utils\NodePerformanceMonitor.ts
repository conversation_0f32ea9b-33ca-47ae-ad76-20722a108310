/**
 * 视觉脚本节点性能监控工具
 * 提供节点执行时间统计和性能分析功能
 */

export interface PerformanceMetrics {
  nodeId: string;
  nodeType: string;
  executionTime: number;
  timestamp: number;
  memoryUsage?: number;
  inputCount: number;
  outputCount: number;
}

export interface PerformanceStats {
  totalExecutions: number;
  averageExecutionTime: number;
  minExecutionTime: number;
  maxExecutionTime: number;
  totalExecutionTime: number;
  lastExecutionTime: number;
  errorCount: number;
}

/**
 * 节点性能监控器
 */
export class NodePerformanceMonitor {
  private static instance: NodePerformanceMonitor;
  private metrics: PerformanceMetrics[] = [];
  private maxMetrics: number = 10000;
  private enabled: boolean = true;

  private constructor() {}

  public static getInstance(): NodePerformanceMonitor {
    if (!NodePerformanceMonitor.instance) {
      NodePerformanceMonitor.instance = new NodePerformanceMonitor();
    }
    return NodePerformanceMonitor.instance;
  }

  /**
   * 启用或禁用性能监控
   * @param enabled 是否启用
   */
  public setEnabled(enabled: boolean): void {
    this.enabled = enabled;
  }

  /**
   * 记录节点执行指标
   * @param nodeId 节点ID
   * @param nodeType 节点类型
   * @param executionTime 执行时间（毫秒）
   * @param inputCount 输入数量
   * @param outputCount 输出数量
   */
  public recordMetrics(
    nodeId: string,
    nodeType: string,
    executionTime: number,
    inputCount: number = 0,
    outputCount: number = 0
  ): void {
    if (!this.enabled) return;

    const metrics: PerformanceMetrics = {
      nodeId,
      nodeType,
      executionTime,
      timestamp: Date.now(),
      inputCount,
      outputCount
    };

    // 记录内存使用情况（如果可用）
    if ((performance as any).memory) {
      metrics.memoryUsage = (performance as any).memory.usedJSHeapSize;
    }

    this.metrics.push(metrics);

    // 限制指标数量
    if (this.metrics.length > this.maxMetrics) {
      this.metrics.shift();
    }

    // 如果执行时间过长，发出警告
    if (executionTime > 100) {
      console.warn(`节点 ${nodeType}:${nodeId} 执行时间过长: ${executionTime}ms`);
    }
  }

  /**
   * 获取节点性能统计
   * @param nodeId 节点ID
   * @returns 性能统计
   */
  public getNodeStats(nodeId: string): PerformanceStats | null {
    const nodeMetrics = this.metrics.filter(m => m.nodeId === nodeId);
    if (nodeMetrics.length === 0) return null;

    const executionTimes = nodeMetrics.map(m => m.executionTime);
    const totalExecutionTime = executionTimes.reduce((sum, time) => sum + time, 0);

    return {
      totalExecutions: nodeMetrics.length,
      averageExecutionTime: totalExecutionTime / nodeMetrics.length,
      minExecutionTime: Math.min(...executionTimes),
      maxExecutionTime: Math.max(...executionTimes),
      totalExecutionTime,
      lastExecutionTime: nodeMetrics[nodeMetrics.length - 1].executionTime,
      errorCount: 0 // 这里可以与错误处理器集成
    };
  }

  /**
   * 获取节点类型性能统计
   * @param nodeType 节点类型
   * @returns 性能统计
   */
  public getNodeTypeStats(nodeType: string): PerformanceStats | null {
    const typeMetrics = this.metrics.filter(m => m.nodeType === nodeType);
    if (typeMetrics.length === 0) return null;

    const executionTimes = typeMetrics.map(m => m.executionTime);
    const totalExecutionTime = executionTimes.reduce((sum, time) => sum + time, 0);

    return {
      totalExecutions: typeMetrics.length,
      averageExecutionTime: totalExecutionTime / typeMetrics.length,
      minExecutionTime: Math.min(...executionTimes),
      maxExecutionTime: Math.max(...executionTimes),
      totalExecutionTime,
      lastExecutionTime: typeMetrics[typeMetrics.length - 1].executionTime,
      errorCount: 0
    };
  }

  /**
   * 获取性能最差的节点
   * @param limit 返回数量限制
   * @returns 性能最差的节点列表
   */
  public getSlowestNodes(limit: number = 10): Array<{ nodeId: string; nodeType: string; stats: PerformanceStats }> {
    const nodeStatsMap = new Map<string, PerformanceStats>();
    
    // 计算每个节点的统计信息
    const uniqueNodeIds = [...new Set(this.metrics.map(m => m.nodeId))];
    uniqueNodeIds.forEach(nodeId => {
      const stats = this.getNodeStats(nodeId);
      if (stats) {
        nodeStatsMap.set(nodeId, stats);
      }
    });

    // 按平均执行时间排序
    const sortedNodes = Array.from(nodeStatsMap.entries())
      .map(([nodeId, stats]) => ({
        nodeId,
        nodeType: this.metrics.find(m => m.nodeId === nodeId)?.nodeType || 'unknown',
        stats
      }))
      .sort((a, b) => b.stats.averageExecutionTime - a.stats.averageExecutionTime);

    return sortedNodes.slice(0, limit);
  }

  /**
   * 获取最近的性能指标
   * @param timeWindow 时间窗口（毫秒）
   * @returns 最近的性能指标
   */
  public getRecentMetrics(timeWindow: number = 60000): PerformanceMetrics[] {
    const cutoffTime = Date.now() - timeWindow;
    return this.metrics.filter(m => m.timestamp >= cutoffTime);
  }

  /**
   * 清除所有性能指标
   */
  public clearMetrics(): void {
    this.metrics = [];
  }

  /**
   * 生成性能报告
   * @returns 性能报告
   */
  public generateReport(): string {
    const totalMetrics = this.metrics.length;
    if (totalMetrics === 0) {
      return '没有性能数据可用';
    }

    const totalExecutionTime = this.metrics.reduce((sum, m) => sum + m.executionTime, 0);
    const averageExecutionTime = totalExecutionTime / totalMetrics;
    
    const nodeTypes = [...new Set(this.metrics.map(m => m.nodeType))];
    const slowestNodes = this.getSlowestNodes(5);

    let report = `性能监控报告\n`;
    report += `================\n`;
    report += `总执行次数: ${totalMetrics}\n`;
    report += `总执行时间: ${totalExecutionTime.toFixed(2)}ms\n`;
    report += `平均执行时间: ${averageExecutionTime.toFixed(2)}ms\n`;
    report += `节点类型数量: ${nodeTypes.length}\n\n`;

    report += `最慢的节点 (前5个):\n`;
    report += `-------------------\n`;
    slowestNodes.forEach((node, index) => {
      report += `${index + 1}. ${node.nodeType}:${node.nodeId} - 平均: ${node.stats.averageExecutionTime.toFixed(2)}ms\n`;
    });

    report += `\n节点类型统计:\n`;
    report += `-------------\n`;
    nodeTypes.forEach(nodeType => {
      const stats = this.getNodeTypeStats(nodeType);
      if (stats) {
        report += `${nodeType}: 执行${stats.totalExecutions}次, 平均${stats.averageExecutionTime.toFixed(2)}ms\n`;
      }
    });

    return report;
  }
}

/**
 * 性能监控装饰器
 */
export function monitorPerformance(target: any, propertyName: string, descriptor: PropertyDescriptor) {
  const method = descriptor.value;

  descriptor.value = function (...args: any[]) {
    const monitor = NodePerformanceMonitor.getInstance();
    const startTime = performance.now();
    
    try {
      const result = method.apply(this, args);
      const endTime = performance.now();
      const executionTime = endTime - startTime;
      
      const nodeId = this.id || 'unknown';
      const nodeType = this.type || 'unknown';
      const inputCount = this.inputs ? Object.keys(this.inputs).length : 0;
      const outputCount = this.outputs ? Object.keys(this.outputs).length : 0;
      
      monitor.recordMetrics(nodeId, nodeType, executionTime, inputCount, outputCount);
      
      return result;
    } catch (error) {
      const endTime = performance.now();
      const executionTime = endTime - startTime;
      
      const nodeId = this.id || 'unknown';
      const nodeType = this.type || 'unknown';
      
      monitor.recordMetrics(nodeId, nodeType, executionTime);
      throw error;
    }
  };
}

/**
 * 批量性能测试工具
 */
export class NodePerformanceTester {
  /**
   * 测试节点性能
   * @param nodeFactory 节点工厂函数
   * @param testData 测试数据
   * @param iterations 迭代次数
   * @returns 测试结果
   */
  public static async testNodePerformance(
    nodeFactory: () => any,
    testData: any[],
    iterations: number = 100
  ): Promise<PerformanceStats> {
    const executionTimes: number[] = [];
    
    for (let i = 0; i < iterations; i++) {
      const node = nodeFactory();
      
      // 设置测试数据
      if (testData.length > 0) {
        const data = testData[i % testData.length];
        Object.keys(data).forEach(key => {
          if (node.setInputValue) {
            node.setInputValue(key, data[key]);
          }
        });
      }
      
      const startTime = performance.now();
      
      try {
        if (node.execute) {
          await node.execute();
        }
      } catch (error) {
        // 忽略执行错误，只关注性能
      }
      
      const endTime = performance.now();
      executionTimes.push(endTime - startTime);
    }
    
    const totalTime = executionTimes.reduce((sum, time) => sum + time, 0);
    
    return {
      totalExecutions: iterations,
      averageExecutionTime: totalTime / iterations,
      minExecutionTime: Math.min(...executionTimes),
      maxExecutionTime: Math.max(...executionTimes),
      totalExecutionTime: totalTime,
      lastExecutionTime: executionTimes[executionTimes.length - 1],
      errorCount: 0
    };
  }
}
