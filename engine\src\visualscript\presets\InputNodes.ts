/**
 * 输入相关的可视化脚本节点
 */

import { FunctionNode } from '../nodes/FunctionNode';
import { EventNode } from '../nodes/EventNode';
import { NodeCategory, SocketType, SocketDirection } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';

/**
 * 键盘输入节点（兼容性保留）
 */
export class KeyboardInputNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    this.addInput({
      name: 'key',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '按键',
      defaultValue: ''
    });

    this.addOutput({
      name: 'pressed',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否按下'
    });

    this.addOutput({
      name: 'justPressed',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '刚按下'
    });

    this.addOutput({
      name: 'justReleased',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '刚释放'
    });
  }

  public execute(): any {
    const key = this.getInputValue('key') as string;
    if (!key) {
      this.setOutputValue('pressed', false);
      this.setOutputValue('justPressed', false);
      this.setOutputValue('justReleased', false);
      return { pressed: false, justPressed: false, justReleased: false };
    }

    // 这里应该连接到实际的输入系统
    // 暂时返回模拟数据
    const result = {
      pressed: false,
      justPressed: false,
      justReleased: false
    };

    this.setOutputValue('pressed', result.pressed);
    this.setOutputValue('justPressed', result.justPressed);
    this.setOutputValue('justReleased', result.justReleased);

    return result;
  }
}

/**
 * 鼠标输入节点（兼容性保留）
 */
export class MouseInputNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    this.addInput({
      name: 'button',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '鼠标按钮',
      defaultValue: 0
    });

    this.addOutput({
      name: 'pressed',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否按下'
    });

    this.addOutput({
      name: 'position',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'vector2',
      description: '鼠标位置'
    });

    this.addOutput({
      name: 'delta',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'vector2',
      description: '移动增量'
    });
  }

  public execute(): any {
    // 这里应该连接到实际的输入系统
    // 暂时返回模拟数据
    const result = {
      pressed: false,
      position: { x: 0, y: 0 },
      delta: { x: 0, y: 0 }
    };

    this.setOutputValue('pressed', result.pressed);
    this.setOutputValue('position', result.position);
    this.setOutputValue('delta', result.delta);

    return result;
  }
}

/**
 * 触摸输入节点（兼容性保留）
 */
export class TouchInputNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    this.addOutput({
      name: 'touching',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否触摸'
    });

    this.addOutput({
      name: 'position',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'vector2',
      description: '触摸位置'
    });

    this.addOutput({
      name: 'touchCount',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '触摸点数量'
    });
  }

  public execute(): any {
    // 这里应该连接到实际的输入系统
    // 暂时返回模拟数据
    const result = {
      touching: false,
      position: { x: 0, y: 0 },
      touchCount: 0
    };

    this.setOutputValue('touching', result.touching);
    this.setOutputValue('position', result.position);
    this.setOutputValue('touchCount', result.touchCount);

    return result;
  }
}

/**
 * 游戏手柄输入节点（兼容性保留）
 */
export class GamepadInputNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    this.addInput({
      name: 'gamepadIndex',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '手柄索引',
      defaultValue: 0
    });

    this.addInput({
      name: 'button',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '按钮',
      defaultValue: 0
    });

    this.addOutput({
      name: 'pressed',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否按下'
    });

    this.addOutput({
      name: 'leftStick',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'vector2',
      description: '左摇杆'
    });

    this.addOutput({
      name: 'rightStick',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'vector2',
      description: '右摇杆'
    });
  }

  public execute(): any {
    // 这里应该连接到实际的输入系统
    // 暂时返回模拟数据
    const result = {
      pressed: false,
      leftStick: { x: 0, y: 0 },
      rightStick: { x: 0, y: 0 }
    };

    this.setOutputValue('pressed', result.pressed);
    this.setOutputValue('leftStick', result.leftStick);
    this.setOutputValue('rightStick', result.rightStick);

    return result;
  }
}

/**
 * 按键按下节点 (150)
 */
export class IsKeyDownNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加按键输入
    this.addInput({
      name: 'key',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '按键代码',
      defaultValue: 'Space'
    });

    // 添加输出插槽
    this.addOutput({
      name: 'isDown',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否按下'
    });

    this.addOutput({
      name: 'justPressed',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '刚刚按下'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const key = this.getInputValue('key') as string;

    if (!key) {
      this.setOutputValue('isDown', false);
      this.setOutputValue('justPressed', false);
      return { isDown: false, justPressed: false };
    }

    try {
      // 这里应该连接到实际的输入系统
      // 暂时使用浏览器的键盘API模拟
      const isDown = this.isKeyCurrentlyDown(key);
      const justPressed = this.isKeyJustPressed(key);

      this.setOutputValue('isDown', isDown);
      this.setOutputValue('justPressed', justPressed);

      return { isDown, justPressed };
    } catch (error) {
      console.error('检查按键状态失败:', error);
      this.setOutputValue('isDown', false);
      this.setOutputValue('justPressed', false);
      return { isDown: false, justPressed: false };
    }
  }

  /**
   * 检查按键是否当前按下
   * @param key 按键代码
   * @returns 是否按下
   */
  private isKeyCurrentlyDown(key: string): boolean {
    // 这里应该连接到实际的输入管理器
    // 暂时返回false
    return false;
  }

  /**
   * 检查按键是否刚刚按下
   * @param key 按键代码
   * @returns 是否刚刚按下
   */
  private isKeyJustPressed(key: string): boolean {
    // 这里应该连接到实际的输入管理器
    // 暂时返回false
    return false;
  }
}

/**
 * 按键释放节点 (151)
 */
export class IsKeyUpNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加按键输入
    this.addInput({
      name: 'key',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '按键代码',
      defaultValue: 'Space'
    });

    // 添加输出插槽
    this.addOutput({
      name: 'isUp',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否释放'
    });

    this.addOutput({
      name: 'justReleased',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '刚刚释放'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const key = this.getInputValue('key') as string;

    if (!key) {
      this.setOutputValue('isUp', true);
      this.setOutputValue('justReleased', false);
      return { isUp: true, justReleased: false };
    }

    try {
      // 这里应该连接到实际的输入系统
      const isUp = this.isKeyCurrentlyUp(key);
      const justReleased = this.isKeyJustReleased(key);

      this.setOutputValue('isUp', isUp);
      this.setOutputValue('justReleased', justReleased);

      return { isUp, justReleased };
    } catch (error) {
      console.error('检查按键状态失败:', error);
      this.setOutputValue('isUp', true);
      this.setOutputValue('justReleased', false);
      return { isUp: true, justReleased: false };
    }
  }

  /**
   * 检查按键是否当前释放
   * @param key 按键代码
   * @returns 是否释放
   */
  private isKeyCurrentlyUp(key: string): boolean {
    // 这里应该连接到实际的输入管理器
    // 暂时返回true
    return true;
  }

  /**
   * 检查按键是否刚刚释放
   * @param key 按键代码
   * @returns 是否刚刚释放
   */
  private isKeyJustReleased(key: string): boolean {
    // 这里应该连接到实际的输入管理器
    // 暂时返回false
    return false;
  }
}

// ==================== 新增输入处理节点 (152-165) ====================

/**
 * 按键事件节点 (152)
 * 监听按键按下事件
 */
export class OnKeyPressNode extends EventNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加按键输入
    this.addInput({
      name: 'key',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '按键代码',
      defaultValue: 'Space'
    });

    // 添加输出插槽
    this.addOutput({
      name: 'onPressed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      dataType: 'void',
      description: '按下时触发'
    });

    this.addOutput({
      name: 'key',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '按键'
    });

    this.addOutput({
      name: 'keyCode',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '键码'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const key = this.getInputValue('key') as string;

    if (!key) {
      return null;
    }

    try {
      // 监听按键事件
      const handleKeyPress = (event: KeyboardEvent) => {
        if (event.code === key || event.key === key) {
          this.setOutputValue('key', event.key);
          this.setOutputValue('keyCode', event.keyCode);
          this.trigger();
        }
      };

      // 添加事件监听器
      document.addEventListener('keydown', handleKeyPress);

      // 返回清理函数
      return () => {
        document.removeEventListener('keydown', handleKeyPress);
      };
    } catch (error) {
      console.error('按键事件监听失败:', error);
      return null;
    }
  }
}

/**
 * 鼠标位置节点 (153)
 * 获取鼠标当前位置
 */
export class GetMousePositionNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输出插槽
    this.addOutput({
      name: 'position',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '位置'
    });

    this.addOutput({
      name: 'x',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: 'X坐标'
    });

    this.addOutput({
      name: 'y',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: 'Y坐标'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    try {
      // 获取当前鼠标位置（相对于视口）
      const mouseX = (window as any).mouseX || 0;
      const mouseY = (window as any).mouseY || 0;

      const position = { x: mouseX, y: mouseY };

      this.setOutputValue('position', position);
      this.setOutputValue('x', mouseX);
      this.setOutputValue('y', mouseY);

      return position;
    } catch (error) {
      console.error('获取鼠标位置失败:', error);
      const defaultPos = { x: 0, y: 0 };
      this.setOutputValue('position', defaultPos);
      this.setOutputValue('x', 0);
      this.setOutputValue('y', 0);
      return defaultPos;
    }
  }
}

/**
 * 鼠标按下节点 (154)
 * 检查鼠标按钮是否被按下
 */
export class IsMouseButtonDownNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加按钮输入
    this.addInput({
      name: 'button',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '按钮索引',
      defaultValue: 0
    });

    // 添加输出插槽
    this.addOutput({
      name: 'isDown',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否按下'
    });

    this.addOutput({
      name: 'button',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '按钮'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const button = this.getInputValue('button') as number;

    try {
      // 这里应该连接到实际的输入系统
      // 暂时使用简单的状态检查
      const isDown = this.isMouseButtonCurrentlyDown(button);

      this.setOutputValue('isDown', isDown);
      this.setOutputValue('button', button);

      return { isDown, button };
    } catch (error) {
      console.error('检查鼠标按钮状态失败:', error);
      this.setOutputValue('isDown', false);
      this.setOutputValue('button', button);
      return { isDown: false, button };
    }
  }

  private isMouseButtonCurrentlyDown(button: number): boolean {
    // 这里应该连接到实际的输入管理器
    // 暂时返回false
    return false;
  }
}

/**
 * 鼠标点击节点 (155)
 * 监听鼠标点击事件
 */
export class OnMouseClickNode extends EventNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加按钮输入
    this.addInput({
      name: 'button',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '按钮索引',
      defaultValue: 0
    });

    // 添加输出插槽
    this.addOutput({
      name: 'onClick',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      dataType: 'void',
      description: '点击时触发'
    });

    this.addOutput({
      name: 'position',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '位置'
    });

    this.addOutput({
      name: 'button',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '按钮'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const targetButton = this.getInputValue('button') as number;

    try {
      // 监听鼠标点击事件
      const handleClick = (event: MouseEvent) => {
        if (event.button === targetButton) {
          const position = { x: event.clientX, y: event.clientY };

          this.setOutputValue('position', position);
          this.setOutputValue('button', event.button);
          this.trigger();
        }
      };

      // 添加事件监听器
      document.addEventListener('click', handleClick);

      // 返回清理函数
      return () => {
        document.removeEventListener('click', handleClick);
      };
    } catch (error) {
      console.error('鼠标点击事件监听失败:', error);
      return null;
    }
  }
}

/**
 * 鼠标移动节点 (156)
 * 监听鼠标移动事件
 */
export class OnMouseMoveNode extends EventNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输出插槽
    this.addOutput({
      name: 'onMove',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      dataType: 'void',
      description: '移动时触发'
    });

    this.addOutput({
      name: 'position',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '位置'
    });

    this.addOutput({
      name: 'deltaX',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: 'X偏移'
    });

    this.addOutput({
      name: 'deltaY',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: 'Y偏移'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    try {
      let lastX = 0;
      let lastY = 0;

      // 监听鼠标移动事件
      const handleMouseMove = (event: MouseEvent) => {
        const position = { x: event.clientX, y: event.clientY };
        const deltaX = event.clientX - lastX;
        const deltaY = event.clientY - lastY;

        this.setOutputValue('position', position);
        this.setOutputValue('deltaX', deltaX);
        this.setOutputValue('deltaY', deltaY);
        this.trigger();

        lastX = event.clientX;
        lastY = event.clientY;
      };

      // 添加事件监听器
      document.addEventListener('mousemove', handleMouseMove);

      // 返回清理函数
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
      };
    } catch (error) {
      console.error('鼠标移动事件监听失败:', error);
      return null;
    }
  }
}

/**
 * 鼠标滚轮节点 (157)
 * 监听鼠标滚轮事件
 */
export class OnMouseWheelNode extends EventNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输出插槽
    this.addOutput({
      name: 'onWheel',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      dataType: 'void',
      description: '滚轮时触发'
    });

    this.addOutput({
      name: 'deltaY',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: 'Y滚动'
    });

    this.addOutput({
      name: 'deltaX',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: 'X滚动'
    });

    this.addOutput({
      name: 'position',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '位置'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    try {
      // 监听鼠标滚轮事件
      const handleWheel = (event: WheelEvent) => {
        const position = { x: event.clientX, y: event.clientY };

        this.setOutputValue('deltaY', event.deltaY);
        this.setOutputValue('deltaX', event.deltaX);
        this.setOutputValue('position', position);
        this.trigger();
      };

      // 添加事件监听器
      document.addEventListener('wheel', handleWheel);

      // 返回清理函数
      return () => {
        document.removeEventListener('wheel', handleWheel);
      };
    } catch (error) {
      console.error('鼠标滚轮事件监听失败:', error);
      return null;
    }
  }
}

/**
 * 触摸点数量节点 (158)
 * 获取当前触摸点数量
 */
export class GetTouchCountNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输出插槽
    this.addOutput({
      name: 'count',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '数量'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    try {
      // 获取当前触摸点数量
      const touchCount = this.getCurrentTouchCount();

      this.setOutputValue('count', touchCount);
      return touchCount;
    } catch (error) {
      console.error('获取触摸点数量失败:', error);
      this.setOutputValue('count', 0);
      return 0;
    }
  }

  private getCurrentTouchCount(): number {
    // 这里应该连接到实际的触摸输入系统
    // 暂时返回0
    return 0;
  }
}

/**
 * 触摸位置节点 (159)
 * 获取指定触摸点位置
 */
export class GetTouchPositionNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加触摸索引输入
    this.addInput({
      name: 'touchIndex',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '触摸索引',
      defaultValue: 0
    });

    // 添加输出插槽
    this.addOutput({
      name: 'position',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '位置'
    });

    this.addOutput({
      name: 'x',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: 'X坐标'
    });

    this.addOutput({
      name: 'y',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: 'Y坐标'
    });

    this.addOutput({
      name: 'isValid',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '有效'
    });
  }

  public execute(): any {
    const touchIndex = this.getInputValue('touchIndex') as number;

    try {
      // 获取指定触摸点位置
      const touchPosition = this.getTouchPosition(touchIndex);

      if (touchPosition) {
        this.setOutputValue('position', touchPosition);
        this.setOutputValue('x', touchPosition.x);
        this.setOutputValue('y', touchPosition.y);
        this.setOutputValue('isValid', true);
        return touchPosition;
      } else {
        const defaultPos = { x: 0, y: 0 };
        this.setOutputValue('position', defaultPos);
        this.setOutputValue('x', 0);
        this.setOutputValue('y', 0);
        this.setOutputValue('isValid', false);
        return defaultPos;
      }
    } catch (error) {
      console.error('获取触摸位置失败:', error);
      const defaultPos = { x: 0, y: 0 };
      this.setOutputValue('position', defaultPos);
      this.setOutputValue('x', 0);
      this.setOutputValue('y', 0);
      this.setOutputValue('isValid', false);
      return defaultPos;
    }
  }

  private getTouchPosition(index: number): { x: number, y: number } | null {
    // 这里应该连接到实际的触摸输入系统
    // 暂时返回null
    return null;
  }
}

/**
 * 触摸开始节点 (160)
 * 监听触摸开始事件
 */
export class OnTouchStartNode extends EventNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输出插槽
    this.addOutput({
      name: 'onTouchStart',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      dataType: 'void',
      description: '触摸开始时触发'
    });

    this.addOutput({
      name: 'position',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '位置'
    });

    this.addOutput({
      name: 'touchId',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '触摸ID'
    });

    this.addOutput({
      name: 'touchCount',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '触摸数量'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    try {
      // 监听触摸开始事件
      const handleTouchStart = (event: TouchEvent) => {
        const touch = event.touches[0];
        if (touch) {
          const position = { x: touch.clientX, y: touch.clientY };

          this.setOutputValue('position', position);
          this.setOutputValue('touchId', touch.identifier);
          this.setOutputValue('touchCount', event.touches.length);
          this.trigger();
        }
      };

      // 添加事件监听器
      document.addEventListener('touchstart', handleTouchStart);

      // 返回清理函数
      return () => {
        document.removeEventListener('touchstart', handleTouchStart);
      };
    } catch (error) {
      console.error('触摸开始事件监听失败:', error);
      return null;
    }
  }
}

/**
 * 触摸结束节点 (161)
 * 监听触摸结束事件
 */
export class OnTouchEndNode extends EventNode {
  protected initializeSockets(): void {
    this.addOutput({
      name: 'onTouchEnd',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      dataType: 'void',
      description: '触摸结束时触发'
    });

    this.addOutput({
      name: 'position',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '位置'
    });

    this.addOutput({
      name: 'touchId',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '触摸ID'
    });

    this.addOutput({
      name: 'touchCount',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '触摸数量'
    });
  }

  public execute(): any {
    try {
      const handleTouchEnd = (event: TouchEvent) => {
        const touch = event.changedTouches[0];
        if (touch) {
          const position = { x: touch.clientX, y: touch.clientY };
          this.setOutputValue('position', position);
          this.setOutputValue('touchId', touch.identifier);
          this.setOutputValue('touchCount', event.touches.length);
          this.trigger();
        }
      };

      document.addEventListener('touchend', handleTouchEnd);
      return () => document.removeEventListener('touchend', handleTouchEnd);
    } catch (error) {
      console.error('触摸结束事件监听失败:', error);
      return null;
    }
  }
}

/**
 * 触摸移动节点 (162)
 * 监听触摸移动事件
 */
export class OnTouchMoveNode extends EventNode {
  protected initializeSockets(): void {
    this.addOutput({
      name: 'onTouchMove',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      dataType: 'void',
      description: '触摸移动时触发'
    });

    this.addOutput({
      name: 'position',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '位置'
    });

    this.addOutput({
      name: 'deltaX',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: 'X偏移'
    });

    this.addOutput({
      name: 'deltaY',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: 'Y偏移'
    });

    this.addOutput({
      name: 'touchId',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '触摸ID'
    });
  }

  public execute(): any {
    try {
      const lastPositions = new Map<number, { x: number, y: number }>();
      const handleTouchMove = (event: TouchEvent) => {
        const touch = event.touches[0];
        if (touch) {
          const position = { x: touch.clientX, y: touch.clientY };
          const lastPos = lastPositions.get(touch.identifier) || position;
          const deltaX = position.x - lastPos.x;
          const deltaY = position.y - lastPos.y;

          this.setOutputValue('position', position);
          this.setOutputValue('deltaX', deltaX);
          this.setOutputValue('deltaY', deltaY);
          this.setOutputValue('touchId', touch.identifier);
          this.trigger();

          lastPositions.set(touch.identifier, position);
        }
      };

      document.addEventListener('touchmove', handleTouchMove);
      return () => document.removeEventListener('touchmove', handleTouchMove);
    } catch (error) {
      console.error('触摸移动事件监听失败:', error);
      return null;
    }
  }
}

/**
 * 手柄连接节点 (163)
 * 检查手柄是否连接
 */
export class IsGamepadConnectedNode extends FunctionNode {
  protected initializeSockets(): void {
    this.addInput({
      name: 'gamepadIndex',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '手柄索引',
      defaultValue: 0
    });

    this.addOutput({
      name: 'isConnected',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否连接'
    });

    this.addOutput({
      name: 'gamepadId',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '手柄ID'
    });

    this.addOutput({
      name: 'buttonCount',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '按钮数量'
    });

    this.addOutput({
      name: 'axisCount',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '轴数量'
    });
  }

  public execute(): any {
    const gamepadIndex = this.getInputValue('gamepadIndex') as number;
    try {
      const gamepad = navigator.getGamepads()[gamepadIndex];
      const isConnected = gamepad !== null && gamepad !== undefined;

      if (isConnected && gamepad) {
        this.setOutputValue('isConnected', true);
        this.setOutputValue('gamepadId', gamepad.id);
        this.setOutputValue('buttonCount', gamepad.buttons.length);
        this.setOutputValue('axisCount', gamepad.axes.length);
        return { isConnected: true, gamepadId: gamepad.id, buttonCount: gamepad.buttons.length, axisCount: gamepad.axes.length };
      } else {
        this.setOutputValue('isConnected', false);
        this.setOutputValue('gamepadId', '');
        this.setOutputValue('buttonCount', 0);
        this.setOutputValue('axisCount', 0);
        return { isConnected: false, gamepadId: '', buttonCount: 0, axisCount: 0 };
      }
    } catch (error) {
      console.error('检查手柄连接状态失败:', error);
      this.setOutputValue('isConnected', false);
      this.setOutputValue('gamepadId', '');
      this.setOutputValue('buttonCount', 0);
      this.setOutputValue('axisCount', 0);
      return { isConnected: false, gamepadId: '', buttonCount: 0, axisCount: 0 };
    }
  }
}

/**
 * 手柄按钮节点 (164)
 * 获取手柄按钮状态
 */
export class GetGamepadButtonStateNode extends FunctionNode {
  protected initializeSockets(): void {
    this.addInput({
      name: 'gamepadIndex',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '手柄索引',
      defaultValue: 0
    });

    this.addInput({
      name: 'buttonIndex',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '按钮索引',
      defaultValue: 0
    });

    this.addOutput({
      name: 'isPressed',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否按下'
    });

    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '按压值'
    });

    this.addOutput({
      name: 'touched',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否触摸'
    });
  }

  public execute(): any {
    const gamepadIndex = this.getInputValue('gamepadIndex') as number;
    const buttonIndex = this.getInputValue('buttonIndex') as number;

    try {
      const gamepad = navigator.getGamepads()[gamepadIndex];
      if (gamepad && gamepad.buttons[buttonIndex]) {
        const button = gamepad.buttons[buttonIndex];
        this.setOutputValue('isPressed', button.pressed);
        this.setOutputValue('value', button.value);
        this.setOutputValue('touched', button.touched);
        return { isPressed: button.pressed, value: button.value, touched: button.touched };
      } else {
        this.setOutputValue('isPressed', false);
        this.setOutputValue('value', 0);
        this.setOutputValue('touched', false);
        return { isPressed: false, value: 0, touched: false };
      }
    } catch (error) {
      console.error('获取手柄按钮状态失败:', error);
      this.setOutputValue('isPressed', false);
      this.setOutputValue('value', 0);
      this.setOutputValue('touched', false);
      return { isPressed: false, value: 0, touched: false };
    }
  }
}

/**
 * 手柄摇杆节点 (165)
 * 获取手柄摇杆轴值
 */
export class GetGamepadAxisValueNode extends FunctionNode {
  protected initializeSockets(): void {
    this.addInput({
      name: 'gamepadIndex',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '手柄索引',
      defaultValue: 0
    });

    this.addInput({
      name: 'axisIndex',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '轴索引',
      defaultValue: 0
    });

    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '轴值'
    });

    this.addOutput({
      name: 'normalizedValue',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '标准化值'
    });
  }

  public execute(): any {
    const gamepadIndex = this.getInputValue('gamepadIndex') as number;
    const axisIndex = this.getInputValue('axisIndex') as number;

    try {
      const gamepad = navigator.getGamepads()[gamepadIndex];
      if (gamepad && gamepad.axes[axisIndex] !== undefined) {
        const value = gamepad.axes[axisIndex];
        const normalizedValue = Math.abs(value) < 0.1 ? 0 : value; // 死区处理
        this.setOutputValue('value', value);
        this.setOutputValue('normalizedValue', normalizedValue);
        return { value: value, normalizedValue: normalizedValue };
      } else {
        this.setOutputValue('value', 0);
        this.setOutputValue('normalizedValue', 0);
        return { value: 0, normalizedValue: 0 };
      }
    } catch (error) {
      console.error('获取手柄摇杆轴值失败:', error);
      this.setOutputValue('value', 0);
      this.setOutputValue('normalizedValue', 0);
      return { value: 0, normalizedValue: 0 };
    }
  }
}

/**
 * 注册输入节点
 * @param registry 节点注册表
 */
export function registerInputNodes(registry: NodeRegistry): void {
  // 注册原有的输入节点（保持兼容性）
  registry.registerNodeType({
    type: 'input/keyboard',
    category: NodeCategory.INPUT,
    constructor: KeyboardInputNode,
    label: '键盘输入',
    description: '获取键盘输入状态',
    icon: 'keyboard',
    color: '#1890FF',
    tags: ['input', 'keyboard']
  });

  registry.registerNodeType({
    type: 'input/mouse',
    category: NodeCategory.INPUT,
    constructor: MouseInputNode,
    label: '鼠标输入',
    description: '获取鼠标输入状态',
    icon: 'mouse',
    color: '#1890FF',
    tags: ['input', 'mouse']
  });

  registry.registerNodeType({
    type: 'input/touch',
    category: NodeCategory.INPUT,
    constructor: TouchInputNode,
    label: '触摸输入',
    description: '获取触摸输入状态',
    icon: 'touch',
    color: '#1890FF',
    tags: ['input', 'touch']
  });

  registry.registerNodeType({
    type: 'input/gamepad',
    category: NodeCategory.INPUT,
    constructor: GamepadInputNode,
    label: '手柄输入',
    description: '获取手柄输入状态',
    icon: 'gamepad',
    color: '#1890FF',
    tags: ['input', 'gamepad']
  });

  // 注册新的按键节点 (150-151)
  registry.registerNodeType({
    type: 'input/keyboard/isKeyDown',
    category: NodeCategory.INPUT,
    constructor: IsKeyDownNode,
    label: '按键按下',
    description: '检查指定按键是否被按下',
    icon: 'key-down',
    color: '#1890FF',
    tags: ['input', 'keyboard', 'key', 'down']
  });

  registry.registerNodeType({
    type: 'input/keyboard/isKeyUp',
    category: NodeCategory.INPUT,
    constructor: IsKeyUpNode,
    label: '按键释放',
    description: '检查指定按键是否被释放',
    icon: 'key-up',
    color: '#1890FF',
    tags: ['input', 'keyboard', 'key', 'up']
  });

  // 注册新的输入处理节点 (152-165)
  registry.registerNodeType({
    type: 'input/keyboard/onKeyPress',
    category: NodeCategory.INPUT,
    constructor: OnKeyPressNode,
    label: '按键事件',
    description: '监听按键按下事件',
    icon: 'key-press',
    color: '#1890FF',
    tags: ['input', 'keyboard', 'event', 'press']
  });

  registry.registerNodeType({
    type: 'input/mouse/getPosition',
    category: NodeCategory.INPUT,
    constructor: GetMousePositionNode,
    label: '鼠标位置',
    description: '获取鼠标当前位置',
    icon: 'mouse-position',
    color: '#1890FF',
    tags: ['input', 'mouse', 'position']
  });

  registry.registerNodeType({
    type: 'input/mouse/isButtonDown',
    category: NodeCategory.INPUT,
    constructor: IsMouseButtonDownNode,
    label: '鼠标按下',
    description: '检查鼠标按钮是否被按下',
    icon: 'mouse-button',
    color: '#1890FF',
    tags: ['input', 'mouse', 'button', 'down']
  });

  registry.registerNodeType({
    type: 'input/mouse/onClick',
    category: NodeCategory.INPUT,
    constructor: OnMouseClickNode,
    label: '鼠标点击',
    description: '监听鼠标点击事件',
    icon: 'mouse-click',
    color: '#1890FF',
    tags: ['input', 'mouse', 'click', 'event']
  });

  registry.registerNodeType({
    type: 'input/mouse/onMove',
    category: NodeCategory.INPUT,
    constructor: OnMouseMoveNode,
    label: '鼠标移动',
    description: '监听鼠标移动事件',
    icon: 'mouse-move',
    color: '#1890FF',
    tags: ['input', 'mouse', 'move', 'event']
  });

  registry.registerNodeType({
    type: 'input/mouse/onWheel',
    category: NodeCategory.INPUT,
    constructor: OnMouseWheelNode,
    label: '鼠标滚轮',
    description: '监听鼠标滚轮事件',
    icon: 'mouse-wheel',
    color: '#1890FF',
    tags: ['input', 'mouse', 'wheel', 'event']
  });

  registry.registerNodeType({
    type: 'input/touch/getTouchCount',
    category: NodeCategory.INPUT,
    constructor: GetTouchCountNode,
    label: '触摸点数量',
    description: '获取当前触摸点数量',
    icon: 'touch-count',
    color: '#1890FF',
    tags: ['input', 'touch', 'count']
  });

  registry.registerNodeType({
    type: 'input/touch/getTouchPosition',
    category: NodeCategory.INPUT,
    constructor: GetTouchPositionNode,
    label: '触摸位置',
    description: '获取指定触摸点位置',
    icon: 'touch-position',
    color: '#1890FF',
    tags: ['input', 'touch', 'position']
  });

  registry.registerNodeType({
    type: 'input/touch/onTouchStart',
    category: NodeCategory.INPUT,
    constructor: OnTouchStartNode,
    label: '触摸开始',
    description: '监听触摸开始事件',
    icon: 'touch-start',
    color: '#1890FF',
    tags: ['input', 'touch', 'start', 'event']
  });

  registry.registerNodeType({
    type: 'input/touch/onTouchEnd',
    category: NodeCategory.INPUT,
    constructor: OnTouchEndNode,
    label: '触摸结束',
    description: '监听触摸结束事件',
    icon: 'touch-end',
    color: '#1890FF',
    tags: ['input', 'touch', 'end', 'event']
  });

  registry.registerNodeType({
    type: 'input/touch/onTouchMove',
    category: NodeCategory.INPUT,
    constructor: OnTouchMoveNode,
    label: '触摸移动',
    description: '监听触摸移动事件',
    icon: 'touch-move',
    color: '#1890FF',
    tags: ['input', 'touch', 'move', 'event']
  });

  registry.registerNodeType({
    type: 'input/gamepad/isConnected',
    category: NodeCategory.INPUT,
    constructor: IsGamepadConnectedNode,
    label: '手柄连接',
    description: '检查手柄是否连接',
    icon: 'gamepad-connected',
    color: '#1890FF',
    tags: ['input', 'gamepad', 'connected']
  });

  registry.registerNodeType({
    type: 'input/gamepad/getButtonState',
    category: NodeCategory.INPUT,
    constructor: GetGamepadButtonStateNode,
    label: '手柄按钮',
    description: '获取手柄按钮状态',
    icon: 'gamepad-button',
    color: '#1890FF',
    tags: ['input', 'gamepad', 'button', 'state']
  });

  registry.registerNodeType({
    type: 'input/gamepad/getAxisValue',
    category: NodeCategory.INPUT,
    constructor: GetGamepadAxisValueNode,
    label: '手柄摇杆',
    description: '获取手柄摇杆轴值',
    icon: 'gamepad-axis',
    color: '#1890FF',
    tags: ['input', 'gamepad', 'axis', 'value']
  });
}
