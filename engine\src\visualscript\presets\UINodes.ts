/**
 * 视觉脚本UI节点
 * 提供UI交互相关的节点
 */
import { EventNode } from '../nodes/EventNode';
import { FlowNode } from '../nodes/FlowNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeCategory, NodeType, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry, NodeTypeInfo } from '../nodes/NodeRegistry';
import type { NodeOptions } from '../nodes/Node';
import { NodeErrorHandler, ErrorLevel, handleNodeErrors } from '../utils/NodeErrorHandler';

/**
 * 按钮点击事件节点
 * 监听按钮点击事件
 */
export class ButtonClickNode extends EventNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '要监听的按钮元素'
    });
  }

  /**
   * 开始监听事件
   */
  public start(): void {
    const element = this.getInputValue('element') as HTMLElement;
    if (element) {
      element.addEventListener('click', () => {
        this.trigger();
      });
    }
  }
}

/**
 * 创建按钮节点 (229)
 * 创建一个UI按钮
 */
export class CreateButtonNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'text',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '按钮显示的文本',
      defaultValue: '按钮'
    });

    this.addOutput({
      name: 'button',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '创建的按钮对象'
    });
  }

  public execute(): any {
    const text = this.getInputValue('text') as string;

    try {
      const button = document.createElement('button');
      button.textContent = text;
      button.style.position = 'absolute';
      button.style.left = '10px';
      button.style.top = '10px';
      button.style.zIndex = '1000';

      document.body.appendChild(button);

      this.setOutputValue('button', button);
      this.triggerFlow('flow');
      return button;
    } catch (error) {
      this.setOutputValue('button', null);
      this.triggerFlow('flow');
      return null;
    }
  }
}

/**
 * 设置按钮文本节点 (231)
 * 设置按钮显示的文本
 */
export class SetButtonTextNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'button',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '要设置文本的按钮对象'
    });

    this.addInput({
      name: 'text',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '新的按钮文本',
      defaultValue: ''
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功设置文本'
    });
  }

  public execute(): any {
    const button = this.getInputValue('button') as HTMLButtonElement;
    const text = this.getInputValue('text') as string;

    if (!button) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      button.textContent = text;
      this.setOutputValue('success', true);
      this.triggerFlow('flow');
      return true;
    } catch (error) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 设置按钮状态节点 (232)
 * 设置按钮是否可用
 */
export class SetButtonEnabledNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'button',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '要设置状态的按钮对象'
    });

    this.addInput({
      name: 'enabled',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '按钮是否可用',
      defaultValue: true
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功设置状态'
    });
  }

  public execute(): any {
    const button = this.getInputValue('button') as HTMLButtonElement;
    const enabled = this.getInputValue('enabled') as boolean;

    if (!button) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      button.disabled = !enabled;
      this.setOutputValue('success', true);
      this.triggerFlow('flow');
      return true;
    } catch (error) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 创建文本节点 (233)
 * 创建一个UI文本标签
 */
export class CreateTextNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'text',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '显示的文本内容',
      defaultValue: '文本'
    });

    this.addOutput({
      name: 'textElement',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '创建的文本元素对象'
    });
  }

  public execute(): any {
    const text = this.getInputValue('text') as string;

    try {
      const textElement = document.createElement('div');
      textElement.textContent = text;
      textElement.style.position = 'absolute';
      textElement.style.left = '10px';
      textElement.style.top = '50px';
      textElement.style.zIndex = '1000';

      document.body.appendChild(textElement);

      this.setOutputValue('textElement', textElement);
      this.triggerFlow('flow');
      return textElement;
    } catch (error) {
      this.setOutputValue('textElement', null);
      this.triggerFlow('flow');
      return null;
    }
  }
}

/**
 * 输入框变化事件节点
 * 监听输入框内容变化事件
 */
export class InputChangeNode extends EventNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '要监听的输入框元素'
    });

    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '输入框的当前值'
    });
  }

  public start(): void {
    const element = this.getInputValue('element') as HTMLInputElement;
    if (element) {
      element.addEventListener('input', () => {
        this.setOutputValue('value', element.value);
        this.trigger();
      });
    }
  }
}

/**
 * 滑块值变化事件节点
 * 监听滑块值变化事件
 */
export class SliderValueChangeNode extends EventNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '要监听的滑块元素'
    });

    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '滑块的当前值'
    });
  }

  public start(): void {
    const element = this.getInputValue('element') as HTMLInputElement;
    if (element) {
      element.addEventListener('input', () => {
        this.setOutputValue('value', parseFloat(element.value));
        this.trigger();
      });
    }
  }
}

/**
 * 设置文本颜色节点 (234)
 * 设置文本元素的颜色
 */
export class SetTextColorNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '文本元素'
    });

    this.addInput({
      name: 'color',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '颜色值（CSS格式）',
      defaultValue: '#000000'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功设置颜色'
    });
  }

  public execute(): any {
    const element = this.getInputValue('element') as HTMLElement;
    const color = this.getInputValue('color') as string;
    const errorHandler = NodeErrorHandler.getInstance();

    // 验证输入
    const elementValidation = NodeErrorHandler.validateHTMLElement(element, undefined, 'element');
    const colorValidation = NodeErrorHandler.validateInput(color, 'string', true, 'color');

    if (!elementValidation.isValid) {
      errorHandler.logError(this.id, this.type, ErrorLevel.ERROR, '元素验证失败', elementValidation.errors);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    if (!colorValidation.isValid) {
      errorHandler.logError(this.id, this.type, ErrorLevel.ERROR, '颜色验证失败', colorValidation.errors);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      element.style.color = color;
      this.setOutputValue('success', true);
      this.triggerFlow('flow');
      errorHandler.logError(this.id, this.type, ErrorLevel.INFO, '成功设置文本颜色', { color });
      return true;
    } catch (error) {
      errorHandler.logError(this.id, this.type, ErrorLevel.ERROR, '设置文本颜色失败', { color }, error as Error);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 设置文本大小节点 (235)
 * 设置文本元素的字体大小
 */
export class SetTextSizeNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '文本元素'
    });

    this.addInput({
      name: 'size',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '字体大小（像素）',
      defaultValue: 16
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功设置大小'
    });
  }

  public execute(): any {
    const element = this.getInputValue('element') as HTMLElement;
    const size = this.getInputValue('size') as number;

    if (!element || typeof size !== 'number') {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      element.style.fontSize = `${size}px`;
      this.setOutputValue('success', true);
      this.triggerFlow('flow');
      return true;
    } catch (error) {
      console.error('设置文本大小失败:', error);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 创建输入框节点 (236)
 * 创建一个文本输入框
 */
export class CreateInputNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'placeholder',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '占位符文本',
      defaultValue: '请输入...'
    });

    this.addInput({
      name: 'defaultValue',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '默认值',
      defaultValue: ''
    });

    this.addOutput({
      name: 'inputElement',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '创建的输入框元素'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功创建'
    });
  }

  public execute(): any {
    const placeholder = this.getInputValue('placeholder') as string;
    const defaultValue = this.getInputValue('defaultValue') as string;

    try {
      const input = document.createElement('input');
      input.type = 'text';
      input.placeholder = placeholder || '请输入...';
      input.value = defaultValue || '';
      input.style.padding = '8px';
      input.style.border = '1px solid #ccc';
      input.style.borderRadius = '4px';

      // 添加到页面（这里应该根据实际需求调整）
      document.body.appendChild(input);

      this.setOutputValue('inputElement', input);
      this.setOutputValue('success', true);
      this.triggerFlow('flow');
      return input;
    } catch (error) {
      console.error('创建输入框失败:', error);
      this.setOutputValue('inputElement', null);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return null;
    }
  }
}

/**
 * 获取输入框值节点 (237)
 * 获取输入框的当前值
 */
export class GetInputValueNode extends FunctionNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'inputElement',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '输入框元素'
    });

    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '输入框的值'
    });
  }

  public execute(): any {
    const inputElement = this.getInputValue('inputElement') as HTMLInputElement;

    if (!inputElement) {
      this.setOutputValue('value', '');
      return '';
    }

    try {
      const value = inputElement.value || '';
      this.setOutputValue('value', value);
      return value;
    } catch (error) {
      console.error('获取输入框值失败:', error);
      this.setOutputValue('value', '');
      return '';
    }
  }
}

/**
 * 设置输入框值节点 (238)
 * 设置输入框的值
 */
export class SetInputValueNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'inputElement',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '输入框元素'
    });

    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '要设置的值',
      defaultValue: ''
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功设置值'
    });
  }

  public execute(): any {
    const inputElement = this.getInputValue('inputElement') as HTMLInputElement;
    const value = this.getInputValue('value') as string;

    if (!inputElement) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      inputElement.value = value || '';
      this.setOutputValue('success', true);
      this.triggerFlow('flow');
      return true;
    } catch (error) {
      console.error('设置输入框值失败:', error);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 设置元素位置节点 (239)
 * 设置UI元素的位置
 */
export class SetElementPositionNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'element',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: 'UI元素'
    });

    this.addInput({
      name: 'x',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: 'X坐标（像素）',
      defaultValue: 0
    });

    this.addInput({
      name: 'y',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: 'Y坐标（像素）',
      defaultValue: 0
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功设置位置'
    });
  }

  public execute(): any {
    const element = this.getInputValue('element') as HTMLElement;
    const x = this.getInputValue('x') as number;
    const y = this.getInputValue('y') as number;

    if (!element) {
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    try {
      element.style.position = 'absolute';
      element.style.left = `${x}px`;
      element.style.top = `${y}px`;
      this.setOutputValue('success', true);
      this.triggerFlow('flow');
      return true;
    } catch (error) {
      console.error('设置元素位置失败:', error);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 创建滑块节点 (240)
 * 创建一个数值滑块控件
 */
export class CreateSliderNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'min',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '最小值',
      defaultValue: 0
    });

    this.addInput({
      name: 'max',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '最大值',
      defaultValue: 100
    });

    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '初始值',
      defaultValue: 50
    });

    this.addInput({
      name: 'step',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '步长',
      defaultValue: 1
    });

    this.addOutput({
      name: 'sliderElement',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '创建的滑块元素'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功创建'
    });
  }

  public execute(): any {
    const min = this.getInputValue('min') as number;
    const max = this.getInputValue('max') as number;
    const value = this.getInputValue('value') as number;
    const step = this.getInputValue('step') as number;

    try {
      const slider = document.createElement('input');
      slider.type = 'range';
      slider.min = String(min);
      slider.max = String(max);
      slider.value = String(value);
      slider.step = String(step);
      slider.style.width = '200px';
      slider.style.height = '20px';

      // 添加到页面
      document.body.appendChild(slider);

      this.setOutputValue('sliderElement', slider);
      this.setOutputValue('success', true);
      this.triggerFlow('flow');
      return slider;
    } catch (error) {
      console.error('创建滑块失败:', error);
      this.setOutputValue('sliderElement', null);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return null;
    }
  }
}

/**
 * 滑块值变化事件节点 (241)
 * 监听滑块值变化事件
 */
export class SliderChangeEventNode extends EventNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'sliderElement',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '滑块元素'
    });

    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '滑块的当前值'
    });
  }

  public start(): void {
    const sliderElement = this.getInputValue('sliderElement') as HTMLInputElement;
    if (sliderElement) {
      sliderElement.addEventListener('input', () => {
        this.setOutputValue('value', parseFloat(sliderElement.value));
        this.trigger();
      });
    }
  }
}

/**
 * 创建复选框节点 (242)
 * 创建一个复选框控件
 */
export class CreateCheckboxNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'label',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '复选框标签',
      defaultValue: '选项'
    });

    this.addInput({
      name: 'checked',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否选中',
      defaultValue: false
    });

    this.addOutput({
      name: 'checkboxElement',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '创建的复选框元素'
    });

    this.addOutput({
      name: 'labelElement',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '标签元素'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功创建'
    });
  }

  public execute(): any {
    const label = this.getInputValue('label') as string;
    const checked = this.getInputValue('checked') as boolean;

    try {
      // 创建容器
      const container = document.createElement('div');
      container.style.display = 'flex';
      container.style.alignItems = 'center';
      container.style.gap = '8px';

      // 创建复选框
      const checkbox = document.createElement('input');
      checkbox.type = 'checkbox';
      checkbox.checked = checked;

      // 创建标签
      const labelElement = document.createElement('label');
      labelElement.textContent = label;
      labelElement.style.cursor = 'pointer';

      // 组装
      container.appendChild(checkbox);
      container.appendChild(labelElement);

      // 点击标签也能切换复选框
      labelElement.addEventListener('click', () => {
        checkbox.checked = !checkbox.checked;
        checkbox.dispatchEvent(new Event('change'));
      });

      // 添加到页面
      document.body.appendChild(container);

      this.setOutputValue('checkboxElement', checkbox);
      this.setOutputValue('labelElement', labelElement);
      this.setOutputValue('success', true);
      this.triggerFlow('flow');
      return checkbox;
    } catch (error) {
      console.error('创建复选框失败:', error);
      this.setOutputValue('checkboxElement', null);
      this.setOutputValue('labelElement', null);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return null;
    }
  }
}

/**
 * 复选框状态变化事件节点 (243)
 * 监听复选框状态变化事件
 */
export class CheckboxChangeEventNode extends EventNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'checkboxElement',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '复选框元素'
    });

    this.addOutput({
      name: 'checked',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '复选框是否选中'
    });
  }

  public start(): void {
    const checkboxElement = this.getInputValue('checkboxElement') as HTMLInputElement;
    if (checkboxElement) {
      checkboxElement.addEventListener('change', () => {
        this.setOutputValue('checked', checkboxElement.checked);
        this.trigger();
      });
    }
  }
}

/**
 * 创建下拉菜单节点 (244)
 * 创建一个下拉选择菜单
 */
export class CreateSelectNode extends FlowNode {
  constructor(options: NodeOptions) {
    super(options);
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    this.addInput({
      name: 'options',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '选项数组',
      defaultValue: ['选项1', '选项2', '选项3']
    });

    this.addInput({
      name: 'selectedIndex',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '默认选中索引',
      defaultValue: 0
    });

    this.addOutput({
      name: 'selectElement',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '创建的下拉菜单元素'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功创建'
    });
  }

  public execute(): any {
    const options = this.getInputValue('options') as string[];
    const selectedIndex = this.getInputValue('selectedIndex') as number;

    try {
      const select = document.createElement('select');
      select.style.padding = '8px';
      select.style.border = '1px solid #ccc';
      select.style.borderRadius = '4px';
      select.style.minWidth = '150px';

      // 添加选项
      options.forEach((optionText, index) => {
        const option = document.createElement('option');
        option.value = String(index);
        option.textContent = optionText;
        if (index === selectedIndex) {
          option.selected = true;
        }
        select.appendChild(option);
      });

      // 添加到页面
      document.body.appendChild(select);

      this.setOutputValue('selectElement', select);
      this.setOutputValue('success', true);
      this.triggerFlow('flow');
      return select;
    } catch (error) {
      console.error('创建下拉菜单失败:', error);
      this.setOutputValue('selectElement', null);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return null;
    }
  }
}



/**
 * 获取滑块值节点 (242)
 * 获取滑块的当前值
 */
export class GetSliderValueNode extends FunctionNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'slider',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '滑块元素'
    });

    // 输出插槽
    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '滑块当前值'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否获取成功'
    });
  }

  /**
   * 执行节点
   */
  public execute(): any {
    const slider = this.getInputValue('slider') as HTMLInputElement;

    try {
      if (slider && slider.type === 'range') {
        const value = parseFloat(slider.value);
        this.setOutputValue('value', value);
        this.setOutputValue('success', true);
        return value;
      } else {
        console.warn('无效的滑块元素');
        this.setOutputValue('value', 0);
        this.setOutputValue('success', false);
        return 0;
      }
    } catch (error) {
      console.error('获取滑块值失败:', error);
      this.setOutputValue('value', 0);
      this.setOutputValue('success', false);
      return 0;
    }
  }
}

/**
 * 设置滑块值节点 (243)
 * 设置滑块的值
 */
export class SetSliderValueNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'slider',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '滑块元素'
    });

    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '要设置的值'
    });

    // 输出插槽
    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否设置成功'
    });
  }

  /**
   * 执行节点
   */
  public execute(): any {
    const slider = this.getInputValue('slider') as HTMLInputElement;
    const value = this.getInputValue('value') as number;

    try {
      if (slider && slider.type === 'range') {
        const min = parseFloat(slider.min);
        const max = parseFloat(slider.max);
        const clampedValue = Math.max(min, Math.min(max, value));

        slider.value = String(clampedValue);

        // 触发change事件
        const event = new Event('change', { bubbles: true });
        slider.dispatchEvent(event);

        this.setOutputValue('success', true);
        this.triggerFlow('flow');
        return true;
      } else {
        console.warn('无效的滑块元素');
        this.setOutputValue('success', false);
        this.triggerFlow('flow');
        return false;
      }
    } catch (error) {
      console.error('设置滑块值失败:', error);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}



/**
 * 创建图像节点 (245)
 * 创建一个UI图像
 */
export class CreateImageNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'src',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '图像源URL',
      defaultValue: ''
    });

    this.addInput({
      name: 'alt',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '替代文本',
      defaultValue: '图像'
    });

    this.addInput({
      name: 'width',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '图像宽度',
      defaultValue: 100
    });

    this.addInput({
      name: 'height',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '图像高度',
      defaultValue: 100
    });

    // 输出插槽
    this.addOutput({
      name: 'image',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '创建的图像元素'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否创建成功'
    });
  }

  /**
   * 执行节点
   */
  public execute(): any {
    const src = this.getInputValue('src') as string;
    const alt = this.getInputValue('alt') as string;
    const width = this.getInputValue('width') as number;
    const height = this.getInputValue('height') as number;

    try {
      const image = document.createElement('img');
      image.src = src;
      image.alt = alt;
      image.style.width = `${width}px`;
      image.style.height = `${height}px`;
      image.style.objectFit = 'cover';
      image.style.border = '1px solid #ccc';
      image.style.borderRadius = '4px';

      // 添加到页面
      document.body.appendChild(image);

      this.setOutputValue('image', image);
      this.setOutputValue('success', true);
      this.triggerFlow('flow');
      return image;
    } catch (error) {
      console.error('创建图像失败:', error);
      this.setOutputValue('image', null);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return null;
    }
  }
}

/**
 * 设置图像纹理节点 (246)
 * 设置图像显示的纹理
 */
export class SetImageTextureNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'image',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '图像元素'
    });

    this.addInput({
      name: 'src',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '新的图像源URL'
    });

    // 输出插槽
    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否设置成功'
    });
  }

  /**
   * 执行节点
   */
  public execute(): any {
    const image = this.getInputValue('image') as HTMLImageElement;
    const src = this.getInputValue('src') as string;

    try {
      if (image && image.tagName === 'IMG') {
        image.src = src;
        this.setOutputValue('success', true);
        this.triggerFlow('flow');
        return true;
      } else {
        console.warn('无效的图像元素');
        this.setOutputValue('success', false);
        this.triggerFlow('flow');
        return false;
      }
    } catch (error) {
      console.error('设置图像纹理失败:', error);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 创建面板节点 (247)
 * 创建一个UI面板容器
 */
export class CreatePanelNode extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'width',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '面板宽度',
      defaultValue: 300
    });

    this.addInput({
      name: 'height',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '面板高度',
      defaultValue: 200
    });

    this.addInput({
      name: 'backgroundColor',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '背景颜色',
      defaultValue: '#f0f0f0'
    });

    // 输出插槽
    this.addOutput({
      name: 'panel',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '创建的面板元素'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否创建成功'
    });
  }

  /**
   * 执行节点
   */
  public execute(): any {
    const width = this.getInputValue('width') as number;
    const height = this.getInputValue('height') as number;
    const backgroundColor = this.getInputValue('backgroundColor') as string;

    try {
      const panel = document.createElement('div');
      panel.style.width = `${width}px`;
      panel.style.height = `${height}px`;
      panel.style.backgroundColor = backgroundColor;
      panel.style.border = '1px solid #ccc';
      panel.style.borderRadius = '4px';
      panel.style.padding = '10px';
      panel.style.position = 'relative';
      panel.style.overflow = 'auto';

      // 添加到页面
      document.body.appendChild(panel);

      this.setOutputValue('panel', panel);
      this.setOutputValue('success', true);
      this.triggerFlow('flow');
      return panel;
    } catch (error) {
      console.error('创建面板失败:', error);
      this.setOutputValue('panel', null);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return null;
    }
  }
}

/**
 * 添加子控件节点 (248)
 * 向面板添加子控件
 */
export class AddChildToPanel extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'panel',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '父面板元素'
    });

    this.addInput({
      name: 'child',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '要添加的子控件'
    });

    // 输出插槽
    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否添加成功'
    });
  }

  /**
   * 执行节点
   */
  public execute(): any {
    const panel = this.getInputValue('panel') as HTMLElement;
    const child = this.getInputValue('child') as HTMLElement;

    try {
      if (panel && child && panel.appendChild) {
        panel.appendChild(child);
        this.setOutputValue('success', true);
        this.triggerFlow('flow');
        return true;
      } else {
        console.warn('无效的面板或子控件元素');
        this.setOutputValue('success', false);
        this.triggerFlow('flow');
        return false;
      }
    } catch (error) {
      console.error('添加子控件失败:', error);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 移除子控件节点 (249)
 * 从面板移除子控件
 */
export class RemoveChildFromPanel extends FlowNode {
  /**
   * 构造函数
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    super(options);
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'panel',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '父面板元素'
    });

    this.addInput({
      name: 'child',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '要移除的子控件'
    });

    // 输出插槽
    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否移除成功'
    });
  }

  /**
   * 执行节点
   */
  public execute(): any {
    const panel = this.getInputValue('panel') as HTMLElement;
    const child = this.getInputValue('child') as HTMLElement;

    try {
      if (panel && child && panel.removeChild && panel.contains(child)) {
        panel.removeChild(child);
        this.setOutputValue('success', true);
        this.triggerFlow('flow');
        return true;
      } else {
        console.warn('无效的面板或子控件元素，或子控件不在面板中');
        this.setOutputValue('success', false);
        this.triggerFlow('flow');
        return false;
      }
    } catch (error) {
      console.error('移除子控件失败:', error);
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }
  }
}

/**
 * 注册UI节点
 * @param registry 节点注册表
 */
export function registerUINodes(registry: NodeRegistry): void {
  // 注册创建按钮节点 (229)
  registry.registerNodeType({
    type: 'ui/button/create',
    category: NodeCategory.UI,
    constructor: CreateButtonNode,
    label: '创建按钮',
    description: '创建一个UI按钮',
    icon: 'button',
    color: '#EB2F96',
    tags: ['ui', 'button', 'create']
  });

  // 注册按钮点击事件节点 (230)
  registry.registerNodeType({
    type: 'ui/button/onClick',
    category: NodeCategory.UI,
    constructor: ButtonClickNode,
    label: '按钮点击',
    description: '监听按钮点击事件',
    icon: 'click',
    color: '#EB2F96',
    tags: ['ui', 'button', 'click', 'event']
  });

  // 注册设置按钮文本节点 (231)
  registry.registerNodeType({
    type: 'ui/button/setText',
    category: NodeCategory.UI,
    constructor: SetButtonTextNode,
    label: '设置按钮文本',
    description: '设置按钮显示的文本',
    icon: 'text',
    color: '#EB2F96',
    tags: ['ui', 'button', 'text', 'set']
  });

  // 注册设置按钮状态节点 (232)
  registry.registerNodeType({
    type: 'ui/button/setEnabled',
    category: NodeCategory.UI,
    constructor: SetButtonEnabledNode,
    label: '设置按钮状态',
    description: '设置按钮是否可用',
    icon: 'toggle',
    color: '#EB2F96',
    tags: ['ui', 'button', 'enabled', 'set']
  });

  // 注册创建文本节点 (233)
  registry.registerNodeType({
    type: 'ui/text/create',
    category: NodeCategory.UI,
    constructor: CreateTextNode,
    label: '创建文本',
    description: '创建一个UI文本标签',
    icon: 'text',
    color: '#EB2F96',
    tags: ['ui', 'text', 'create']
  });

  // 注册设置文本颜色节点 (234)
  registry.registerNodeType({
    type: 'ui/text/setColor',
    category: NodeCategory.UI,
    constructor: SetTextColorNode,
    label: '设置文本颜色',
    description: '设置文本元素的颜色',
    icon: 'color',
    color: '#EB2F96',
    tags: ['ui', 'text', 'color', 'set']
  });

  // 注册设置文本大小节点 (235)
  registry.registerNodeType({
    type: 'ui/text/setSize',
    category: NodeCategory.UI,
    constructor: SetTextSizeNode,
    label: '设置文本大小',
    description: '设置文本元素的字体大小',
    icon: 'size',
    color: '#EB2F96',
    tags: ['ui', 'text', 'size', 'font', 'set']
  });

  // 注册创建输入框节点 (236)
  registry.registerNodeType({
    type: 'ui/input/create',
    category: NodeCategory.UI,
    constructor: CreateInputNode,
    label: '创建输入框',
    description: '创建一个文本输入框',
    icon: 'input',
    color: '#EB2F96',
    tags: ['ui', 'input', 'create']
  });

  // 注册获取输入框值节点 (237)
  registry.registerNodeType({
    type: 'ui/input/getValue',
    category: NodeCategory.UI,
    constructor: GetInputValueNode,
    label: '获取输入框值',
    description: '获取输入框的当前值',
    icon: 'get',
    color: '#EB2F96',
    tags: ['ui', 'input', 'value', 'get']
  });

  // 注册设置输入框值节点 (238)
  registry.registerNodeType({
    type: 'ui/input/setValue',
    category: NodeCategory.UI,
    constructor: SetInputValueNode,
    label: '设置输入框值',
    description: '设置输入框的值',
    icon: 'set',
    color: '#EB2F96',
    tags: ['ui', 'input', 'value', 'set']
  });

  // 注册设置元素位置节点 (239)
  registry.registerNodeType({
    type: 'ui/element/setPosition',
    category: NodeCategory.UI,
    constructor: SetElementPositionNode,
    label: '设置元素位置',
    description: '设置UI元素的位置',
    icon: 'position',
    color: '#EB2F96',
    tags: ['ui', 'element', 'position', 'set']
  });

  // 注册输入框变化事件节点 (240)
  registry.registerNodeType({
    type: 'ui/input/onChange',
    category: NodeCategory.UI,
    constructor: InputChangeNode,
    label: '输入变化',
    description: '监听输入框内容变化事件',
    icon: 'edit',
    color: '#EB2F96',
    tags: ['ui', 'input', 'change', 'event']
  });

  // 注册创建滑块节点 (241)
  registry.registerNodeType({
    type: 'ui/slider/create',
    category: NodeCategory.UI,
    constructor: CreateSliderNode,
    label: '创建滑块',
    description: '创建一个滑块控件',
    icon: 'slider',
    color: '#EB2F96',
    tags: ['ui', 'slider', 'create']
  });

  // 注册获取滑块值节点 (242)
  registry.registerNodeType({
    type: 'ui/slider/getValue',
    category: NodeCategory.UI,
    constructor: GetSliderValueNode,
    label: '获取滑块值',
    description: '获取滑块的当前值',
    icon: 'get',
    color: '#EB2F96',
    tags: ['ui', 'slider', 'value', 'get']
  });

  // 注册设置滑块值节点 (243)
  registry.registerNodeType({
    type: 'ui/slider/setValue',
    category: NodeCategory.UI,
    constructor: SetSliderValueNode,
    label: '设置滑块值',
    description: '设置滑块的值',
    icon: 'set',
    color: '#EB2F96',
    tags: ['ui', 'slider', 'value', 'set']
  });

  // 注册滑块值变化事件节点 (244)
  registry.registerNodeType({
    type: 'ui/slider/onValueChange',
    category: NodeCategory.UI,
    constructor: SliderValueChangeNode,
    label: '滑块值变化',
    description: '监听滑块值变化事件',
    icon: 'event',
    color: '#EB2F96',
    tags: ['ui', 'slider', 'change', 'event']
  });

  // 注册创建图像节点 (245)
  registry.registerNodeType({
    type: 'ui/image/create',
    category: NodeCategory.UI,
    constructor: CreateImageNode,
    label: '创建图像',
    description: '创建一个UI图像',
    icon: 'image',
    color: '#EB2F96',
    tags: ['ui', 'image', 'create']
  });

  // 注册设置图像纹理节点 (246)
  registry.registerNodeType({
    type: 'ui/image/setTexture',
    category: NodeCategory.UI,
    constructor: SetImageTextureNode,
    label: '设置图像纹理',
    description: '设置图像显示的纹理',
    icon: 'texture',
    color: '#EB2F96',
    tags: ['ui', 'image', 'texture', 'set']
  });

  // 注册创建面板节点 (247)
  registry.registerNodeType({
    type: 'ui/panel/create',
    category: NodeCategory.UI,
    constructor: CreatePanelNode,
    label: '创建面板',
    description: '创建一个UI面板容器',
    icon: 'panel',
    color: '#EB2F96',
    tags: ['ui', 'panel', 'container', 'create']
  });

  // 注册添加子控件节点 (248)
  registry.registerNodeType({
    type: 'ui/panel/addChild',
    category: NodeCategory.UI,
    constructor: AddChildToPanel,
    label: '添加子控件',
    description: '向面板添加子控件',
    icon: 'add',
    color: '#EB2F96',
    tags: ['ui', 'panel', 'child', 'add']
  });

  // 注册移除子控件节点 (249)
  registry.registerNodeType({
    type: 'ui/panel/removeChild',
    category: NodeCategory.UI,
    constructor: RemoveChildFromPanel,
    label: '移除子控件',
    description: '从面板移除子控件',
    icon: 'remove',
    color: '#EB2F96',
    tags: ['ui', 'panel', 'child', 'remove']
  });

  // 注册创建复选框节点 (250)
  registry.registerNodeType({
    type: 'ui/checkbox/create',
    category: NodeCategory.UI,
    constructor: CreateCheckboxNode,
    label: '创建复选框',
    description: '创建一个复选框控件',
    icon: 'checkbox',
    color: '#EB2F96',
    tags: ['ui', 'checkbox', 'create']
  });

  // 注册复选框状态变化事件节点 (251)
  registry.registerNodeType({
    type: 'ui/checkbox/onChange',
    category: NodeCategory.UI,
    constructor: CheckboxChangeEventNode,
    label: '复选框状态变化',
    description: '监听复选框状态变化事件',
    icon: 'event',
    color: '#EB2F96',
    tags: ['ui', 'checkbox', 'event', 'change']
  });

  // 注册创建下拉菜单节点 (252)
  registry.registerNodeType({
    type: 'ui/select/create',
    category: NodeCategory.UI,
    constructor: CreateSelectNode,
    label: '创建下拉菜单',
    description: '创建一个下拉选择菜单',
    icon: 'select',
    color: '#EB2F96',
    tags: ['ui', 'select', 'dropdown', 'create']
  });
}
