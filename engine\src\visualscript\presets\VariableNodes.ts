/**
 * 视觉脚本变量节点
 * 提供变量操作相关的节点
 */
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeCategory, NodeType, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry, NodeTypeInfo } from '../nodes/NodeRegistry';

/**
 * 全局变量存储
 */
class VariableStore {
  private static instance: VariableStore;
  private variables: Map<string, any> = new Map();

  public static getInstance(): VariableStore {
    if (!VariableStore.instance) {
      VariableStore.instance = new VariableStore();
    }
    return VariableStore.instance;
  }

  public get(name: string): any {
    return this.variables.get(name);
  }

  public set(name: string, value: any): void {
    this.variables.set(name, value);
  }

  public has(name: string): boolean {
    return this.variables.has(name);
  }

  public delete(name: string): boolean {
    return this.variables.delete(name);
  }

  public clear(): void {
    this.variables.clear();
  }
}

/**
 * 获取变量节点
 * 获取指定名称的变量值
 */
export class VariableGetNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加变量名输入
    this.addInput({
      name: 'name',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '变量名',
      defaultValue: ''
    });

    // 添加默认值输入
    this.addInput({
      name: 'defaultValue',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'any',
      description: '默认值（变量不存在时返回）',
      defaultValue: null
    });

    // 添加结果输出
    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'any',
      description: '变量值'
    });

    // 添加存在性输出
    this.addOutput({
      name: 'exists',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '变量是否存在'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const name = String(this.getInputValue('name') || '');
    const defaultValue = this.getInputValue('defaultValue');

    // 获取变量存储实例
    const store = VariableStore.getInstance();

    // 检查变量是否存在
    const exists = store.has(name);
    const value = exists ? store.get(name) : defaultValue;

    // 设置输出值
    this.setOutputValue('value', value);
    this.setOutputValue('exists', exists);

    return value;
  }
}

/**
 * 设置变量节点
 * 设置指定名称的变量值
 */
export class VariableSetNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加变量名输入
    this.addInput({
      name: 'name',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '变量名',
      defaultValue: ''
    });

    // 添加变量值输入
    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'any',
      description: '变量值',
      defaultValue: null
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });

    // 添加设置的值输出
    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'any',
      description: '设置的值'
    });

    // 添加成功状态输出
    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '设置是否成功'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const name = String(this.getInputValue('name') || '');
    const value = this.getInputValue('value');

    // 获取变量存储实例
    const store = VariableStore.getInstance();

    let success = false;
    try {
      if (name) {
        store.set(name, value);
        success = true;
      }
    } catch (error) {
      console.error('设置变量失败:', error);
      success = false;
    }

    // 设置输出值
    this.setOutputValue('value', value);
    this.setOutputValue('success', success);

    // 触发输出流程
    this.triggerFlow('flow');

    return value;
  }
}

/**
 * 变量自增节点
 * 将变量值增加指定数量
 */
export class VariableIncrementNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加变量名输入
    this.addInput({
      name: 'name',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '变量名',
      defaultValue: ''
    });

    // 添加增量输入
    this.addInput({
      name: 'increment',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '增量',
      defaultValue: 1
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });

    // 添加新值输出
    this.addOutput({
      name: 'newValue',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '新的变量值'
    });

    // 添加旧值输出
    this.addOutput({
      name: 'oldValue',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '原来的变量值'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const name = String(this.getInputValue('name') || '');
    const increment = Number(this.getInputValue('increment') || 1);

    // 获取变量存储实例
    const store = VariableStore.getInstance();

    // 获取当前值
    const oldValue = Number(store.get(name) || 0);
    const newValue = oldValue + increment;

    // 设置新值
    if (name) {
      store.set(name, newValue);
    }

    // 设置输出值
    this.setOutputValue('newValue', newValue);
    this.setOutputValue('oldValue', oldValue);

    // 触发输出流程
    this.triggerFlow('flow');

    return newValue;
  }
}

/**
 * 变量自减节点
 * 将变量值减少指定数量
 */
export class VariableDecrementNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });

    // 添加变量名输入
    this.addInput({
      name: 'name',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '变量名',
      defaultValue: ''
    });

    // 添加减量输入
    this.addInput({
      name: 'decrement',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '减量',
      defaultValue: 1
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });

    // 添加新值输出
    this.addOutput({
      name: 'newValue',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '新的变量值'
    });

    // 添加旧值输出
    this.addOutput({
      name: 'oldValue',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '原来的变量值'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const name = String(this.getInputValue('name') || '');
    const decrement = Number(this.getInputValue('decrement') || 1);

    // 获取变量存储实例
    const store = VariableStore.getInstance();

    // 获取当前值
    const oldValue = Number(store.get(name) || 0);
    const newValue = oldValue - decrement;

    // 设置新值
    if (name) {
      store.set(name, newValue);
    }

    // 设置输出值
    this.setOutputValue('newValue', newValue);
    this.setOutputValue('oldValue', oldValue);

    // 触发输出流程
    this.triggerFlow('flow');

    return newValue;
  }
}

/**
 * 注册变量节点
 * @param registry 节点注册表
 */
export function registerVariableNodes(registry: NodeRegistry): void {
  // 注册获取变量节点
  registry.registerNodeType({
    type: 'variable/get',
    category: NodeCategory.VARIABLE,
    constructor: VariableGetNode,
    label: '获取变量',
    description: '获取指定名称的变量值',
    icon: 'download',
    color: '#FA8C16',
    tags: ['variable', 'get', 'read']
  });

  // 注册设置变量节点
  registry.registerNodeType({
    type: 'variable/set',
    category: NodeCategory.VARIABLE,
    constructor: VariableSetNode,
    label: '设置变量',
    description: '设置指定名称的变量值',
    icon: 'upload',
    color: '#FA8C16',
    tags: ['variable', 'set', 'write']
  });

  // 注册变量自增节点
  registry.registerNodeType({
    type: 'variable/increment',
    category: NodeCategory.VARIABLE,
    constructor: VariableIncrementNode,
    label: '变量自增',
    description: '将变量值增加指定数量',
    icon: 'plus-circle',
    color: '#FA8C16',
    tags: ['variable', 'increment', 'math']
  });

  // 注册变量自减节点
  registry.registerNodeType({
    type: 'variable/decrement',
    category: NodeCategory.VARIABLE,
    constructor: VariableDecrementNode,
    label: '变量自减',
    description: '将变量值减少指定数量',
    icon: 'minus-circle',
    color: '#FA8C16',
    tags: ['variable', 'decrement', 'math']
  });
}
